'use client'

import shareIcon from '@/assets/icons/share-icon.svg'
import { useShare } from '@/hooks/common/useShare'
import { cn } from '@/utilities/cn'
import { Check } from 'lucide-react'
import Image from 'next/image'
import { useCallback, useEffect, useState } from 'react'
import { Button, ButtonProps } from '../ui/Button/Button'

interface ShareLinkButtonProps extends Omit<ButtonProps, 'onClick'> {
  title?: string
  text?: string
  url?: string
  onShareSuccess?: () => void
  onShareError?: (error: unknown) => void
  fallbackToClipboard?: boolean
}

// Moved to a separate utility function
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator?.clipboard?.writeText) {
      await navigator.clipboard.writeText(text)
      return true
    }

    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.cssText = 'position:absolute;left:-9999px'
    document.body.appendChild(textArea)

    try {
      textArea.select()
      document.execCommand('copy')
      return true
    } finally {
      document.body.removeChild(textArea)
    }
  } catch (error) {
    console.error('Copy failed:', error)
    return false
  }
}

export const ShareIcon = () => (
  <Image src={shareIcon} alt="shareIcon" width={20} height={20} className="size-[20px]" priority />
)

export const ShareLinkButton: React.FC<ShareLinkButtonProps> = ({
  title: propTitle = '',
  text = '',
  url: propUrl = '',
  className = '',
  children,
  onShareSuccess,
  onShareError,
  fallbackToClipboard = true,
  ...props
}) => {
  const { share, isShareSupported } = useShare()
  const [copied, setCopied] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [defaultValues, setDefaultValues] = useState({ title: '', url: '' })

  useEffect(() => {
    setMounted(true)
    setDefaultValues({
      title: document.title,
      url: window.location.href,
    })
  }, [])

  const finalTitle = propTitle || defaultValues.title
  const finalUrl = propUrl || defaultValues.url

  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => setCopied(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [copied])

  const handleCopyToClipboard = useCallback(async () => {
    try {
      const success = await copyToClipboard(finalUrl)
      if (success) {
        setCopied(true)
        onShareSuccess?.()
      }
    } catch (error) {
      console.error('Clipboard copy failed:', error)
      onShareError?.(error)
    }
  }, [finalUrl, onShareSuccess, onShareError])

  const handleShare = useCallback(async () => {
    try {
      const success = await share({
        title: finalTitle,
        text,
        url: finalUrl,
      })
      if (success) onShareSuccess?.()
    } catch (error) {
      onShareError?.(error)
    }
  }, [share, finalTitle, text, finalUrl, onShareSuccess, onShareError])

  if (!mounted) {
    return null
  }

  if (!isShareSupported && !fallbackToClipboard) {
    return null
  }

  const buttonContent = children || <ShareIcon />
  const buttonProps = {
    onClick: !isShareSupported && fallbackToClipboard ? handleCopyToClipboard : handleShare,
    className: cn('w-fit h-fit', className),
    variant: 'ghost' as const,
    size: 'icon' as const,
    ...props,
  }

  return (
    <Button {...buttonProps}>
      {!isShareSupported && fallbackToClipboard && copied ? (
        <Check className="h-5 w-5 text-primary" />
      ) : (
        buttonContent
      )}
    </Button>
  )
}

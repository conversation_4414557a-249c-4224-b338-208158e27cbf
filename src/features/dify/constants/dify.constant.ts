import { APP_ROUTES } from '@/routes'

// Dataset IDs for different collections
export const DIFY_DATASETS = {
  POSTS_VI: process.env.DIFY_DATASET_POSTS_VI || '',
  POSTS_JA: process.env.DIFY_DATASET_POSTS_JA || '',
  MEDICINES: process.env.DIFY_DATASET_MEDICINES || '',
  KEYWORDS: process.env.DIFY_DATASET_KEYWORDS || '',
  FACULTIES: process.env.DIFY_DATASET_FACULTIES || '',
  PRODUCTS: process.env.DIFY_DATASET_PRODUCTS || '',
} as const

// Document processing configuration for different collections
export const DIFY_DOCUMENT_CONFIG = {
  POSTS_VI: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '\n',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
  },
  POSTS_JA: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '\n',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
  },
  MEDICINES: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '===',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
    retrival_model: {
      search_method: 'hybrid_search',
      reranking_enable: true,
      reranking_mode: {
        reranking_provider_name: 'langgenius/jina/jina',
        reranking_model_name: 'jina-reranker-v2-base-multilingual',
      },

      top_k: 3,
      score_threshold_enabled: true,
      score_threshold: 0.5,
    },
  },
  KEYWORDS: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '===',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
    retrival_model: {
      search_method: 'hybrid_search',
      reranking_enable: true,
      reranking_mode: {
        reranking_provider_name: 'langgenius/jina/jina',
        reranking_model_name: 'jina-reranker-v2-base-multilingual',
      },
      top_k: 3,
      score_threshold_enabled: true,
      score_threshold: 0.5,
    },
  },
  FACULTIES: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '---',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
    retrival_model: {
      search_method: 'hybrid_search',
      reranking_enable: true,
      reranking_mode: {
        reranking_provider_name: 'langgenius/jina/jina',
        reranking_model_name: 'jina-reranker-v2-base-multilingual',
      },
      top_k: 3,
      score_threshold_enabled: true,
      score_threshold: 0.5,
    },
  },
  PRODUCTS: {
    doc_form: 'hierarchical_model' as const,
    embedding_model: 'dengcao/Qwen3-Embedding-4B:Q8_0',
    embedding_model_provider: 'langgenius/ollama/ollama',
    indexing_technique: 'high_quality' as const,
    process_rule: {
      rules: {
        pre_processing_rules: [
          {
            id: 'remove_extra_spaces',
            enabled: true,
          },
          {
            id: 'remove_urls_emails',
            enabled: false,
          },
        ] as { id: string; enabled: boolean }[],
        segmentation: {
          separator: '\n\n\n\n',
          max_tokens: 4000,
        },
        parent_mode: 'paragraph' as const,
        subchunk_segmentation: {
          separator: '===',
          max_tokens: 512,
        },
      },
      mode: 'hierarchical' as const,
    },
    retrival_model: {
      search_method: 'hybrid_search',
      reranking_enable: true,
      reranking_mode: {
        reranking_provider_name: 'langgenius/jina/jina',
        reranking_model_name: 'jina-reranker-v2-base-multilingual',
      },

      top_k: 3,
      score_threshold_enabled: true,
      score_threshold: 0.5,
    },
  },
}

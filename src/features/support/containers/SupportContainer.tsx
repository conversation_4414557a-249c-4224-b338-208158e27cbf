import messageQuestion from '@/assets/icons/message-question.svg'
import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { SupportFAQs } from '../components/SupportFAQs/SupportFAQs'
import { SupportRequestForm } from '../components/SupportRequestForm/SupportRequestForm'
export const SupportContainer = () => {
  const t = useTranslations()

  return (
    <div className="space-y-3">
      {/* Header Section */}
      <div className="space-y-2 px-4 py-3">
        <div className="flex items-center gap-3">
          <PreviousPageButton title="" />
        </div>
        <h1 className="typo-heading-7 text-primary">{t('MES-668')}</h1>
        <p className="typo-body-7 text-subdued">{t('MES-689')}</p>
      </div>
      {/* Main Content */}
      <div className="space-y-6 px-4">
        {/* FAQ Section */}
        <SupportFAQs />

        {/* Need help */}

        <div className="rounded-lg bg-primary-80 p-4">
          <div className="flex gap-x-2">
            <Image
              src={messageQuestion}
              alt="messageQuestion"
              width={20}
              height={20}
              className="size-5"
            ></Image>
            <div className="space-y-2">
              <p className="typo-body-3">{t('MES-685')}</p>
              <p className="typo-body-6">
                {t.rich('MES-686', {
                  email: (chunks) => (
                    <a
                      className="text-primary underline"
                      href={`mailto:${process.env.NEXT_PUBLIC_SUPPORT_ADMIN_MAIL}`}
                    >
                      {chunks}
                    </a>
                  ),
                })}
              </p>
            </div>
          </div>
        </div>

        {/* Support Request Section */}
        <SupportRequestForm />
      </div>
    </div>
  )
}

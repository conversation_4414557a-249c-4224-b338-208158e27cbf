import { difyService } from '../services/dify.service'
import { APP_ROUTES } from '@/routes'

// Helper function to get metadata from API
async function getDatasetMetadata(datasetId: string) {
  try {
    const response = await difyService.getDatasetMetadata({ datasetId })

    // Convert array to object with field name as key for easy lookup
    const metadataMap: Record<string, { id: string; name: string; type: string; count: number }> =
      {}
    response.doc_metadata.forEach((field) => {
      metadataMap[field.name] = field
    })

    return metadataMap
  } catch (error) {
    console.error(`Failed to fetch metadata for dataset ${datasetId}:`, error)
    return {}
  }
}

// Builder function for Posts  metadata
export async function buildPostsMetadata({
  datasetId,
  docSlug,
  imageUrl,
  locale,
}: {
  datasetId: string
  docSlug: string
  imageUrl?: string
  locale?: string
}) {
  const metadata = await getDatasetMetadata(datasetId)

  const metadataList: Array<{ id: string; name: string; value: string | null }> = []

  // URL field
  if (metadata['url']) {
    metadataList.push({
      id: metadata['url'].id,
      name: metadata['url'].name,
      value: `${process.env.NEXT_PUBLIC_SERVER_URL}/${locale}${APP_ROUTES.POSTS.path}/${docSlug}`,
    })
  }

  // Image URL field
  if (metadata['image_url']) {
    metadataList.push({
      id: metadata['image_url'].id,
      name: metadata['image_url'].name,
      value: imageUrl ? JSON.stringify([imageUrl]) : null,
    })
  }

  // Source Doc field
  if (metadata['source_doc']) {
    metadataList.push({
      id: metadata['source_doc'].id,
      name: metadata['source_doc'].name,
      value: 'post',
    })
  }

  return metadataList
}

// Builder function for Medicines metadata
export async function buildMedicinesMetadata({
  datasetId,
  docSlug,
  imageUrl,
  description,
  isSupplement,
}: {
  datasetId: string
  docSlug: string
  imageUrl?: string
  description?: string
  isSupplement?: boolean
}) {
  const metadata = await getDatasetMetadata(datasetId)

  const metadataList: Array<{ id: string; name: string; value: string }> = []

  // URL field
  if (metadata['url']) {
    metadataList.push({
      id: metadata['url'].id,
      name: metadata['url'].name,
      value: `${process.env.NEXT_PUBLIC_SERVER_URL}${APP_ROUTES.PRODUCTS.path}/${docSlug}${
        !isSupplement ? '?s=false' : ''
      }`,
    })
  }

  // Image URL field
  if (metadata['image_url']) {
    metadataList.push({
      id: metadata['image_url'].id,
      name: metadata['image_url'].name,
      value: imageUrl || '',
    })
  }

  // Source Doc field
  if (metadata['source_doc']) {
    metadataList.push({
      id: metadata['source_doc'].id,
      name: metadata['source_doc'].name,
      value: 'medicine',
    })
  }

  // Description field
  if (metadata['description']) {
    metadataList.push({
      id: metadata['description'].id,
      name: metadata['description'].name,
      value: description || '',
    })
  }

  return metadataList
}

// Builder function for Keywords metadata
export async function buildKeywordsMetadata({ datasetId }: { datasetId: string }) {
  const metadata = await getDatasetMetadata(datasetId)

  const metadataList: Array<{ id: string; name: string; value: string }> = []

  // Source Doc field
  if (metadata['source_doc']) {
    metadataList.push({
      id: metadata['source_doc'].id,
      name: metadata['source_doc'].name,
      value: 'keyword',
    })
  }

  return metadataList
}

// Builder function for Faculties metadata
export async function buildFacultiesMetadata({ datasetId }: { datasetId: string }) {
  const metadata = await getDatasetMetadata(datasetId)

  const metadataList: Array<{ id: string; name: string; value: string }> = []

  // Source Doc field
  if (metadata['source_doc']) {
    metadataList.push({
      id: metadata['source_doc'].id,
      name: metadata['source_doc'].name,
      value: 'keyword',
    })
  }

  return metadataList
}
// Builder function for Products metadata
export async function buildProductsMetadata({
  datasetId,
  docSlug,
  imageUrl,
  description,
}: {
  datasetId: string
  docSlug: string
  imageUrl?: string
  description?: string
}) {
  const metadata = await getDatasetMetadata(datasetId)

  const metadataList: Array<{ id: string; name: string; value: string }> = []

  // URL field
  if (metadata['url']) {
    metadataList.push({
      id: metadata['url'].id,
      name: metadata['url'].name,
      value: `${process.env.NEXT_PUBLIC_SERVER_URL}${APP_ROUTES.PRODUCTS_V2.path}/${docSlug}`,
    })
  }

  // Image URL field
  if (metadata['image_url']) {
    metadataList.push({
      id: metadata['image_url'].id,
      name: metadata['image_url'].name,
      value: imageUrl || '',
    })
  }

  // Source Doc field
  if (metadata['source_doc']) {
    metadataList.push({
      id: metadata['source_doc'].id,
      name: metadata['source_doc'].name,
      value: 'product',
    })
  }

  // Description field
  if (metadata['description']) {
    metadataList.push({
      id: metadata['description'].id,
      name: metadata['description'].name,
      value: description || '',
    })
  }

  return metadataList
}

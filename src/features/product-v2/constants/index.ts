import { LocaleEnum } from '@/enums/locale.enum'
import { ProductV2TypeEnum } from '../enums'
import medicalInstrumentIcon from '@/assets/icons/product-type-medical-instrument-icon.svg'
import dietarySupplementIcon from '@/assets/icons/product-type-dietary-supplement.svg'
import medicineIcon from '@/assets/icons/product-type-medicine-icon.svg'

export const PRODUCT_V2_TYPE_OPTIONS = {
  [ProductV2TypeEnum.MEDICINE]: {
    label: 'Medicine',
    value: ProductV2TypeEnum.MEDICINE,
    adminLabel: {
      [LocaleEnum.VI]: 'Thuốc',
      [LocaleEnum.JA]: '薬',
    },
    translationKey: 'MES-718',
    icon: medicineIcon,
  },
  [ProductV2TypeEnum.DIETARY_SUPPLEMENT]: {
    label: 'Dietary Supplement',
    value: ProductV2TypeEnum.DIETARY_SUPPLEMENT,
    adminLabel: {
      [LocaleEnum.VI]: 'Thức phẩm chức năng',
      [LocaleEnum.JA]: '栄養補助食品',
    },
    translationKey: 'MES-471',
    icon: dietarySupplementIcon,
  },
  [ProductV2TypeEnum.MEDICAL_INSTRUMENT]: {
    label: 'Medical Instrument',
    value: ProductV2TypeEnum.MEDICAL_INSTRUMENT,
    adminLabel: {
      [LocaleEnum.VI]: 'Dụng cụ y khoa',
      [LocaleEnum.JA]: '医療機器',
    },
    translationKey: 'MES-705',
    icon: medicalInstrumentIcon,
  },
}

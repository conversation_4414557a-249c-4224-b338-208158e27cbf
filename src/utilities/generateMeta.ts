import type { Metadata } from 'next'

import type { Config, Media, Medicine, Post, Product } from '../payload-types'

import { MedicineV2 } from '@/types/medicine.type'
import { getServerSideURL } from './getURL'
import { mergeOpenGraph } from './mergeOpenGraph'

const getImageURL = (image?: Media | Config['db']['defaultIDType'] | null) => {
  const serverUrl = getServerSideURL()

  let url = serverUrl

  if (image && typeof image === 'object' && 'url' in image) {
    const ogUrl = image.sizes?.og?.url || image?.thumbnailURL

    url = ogUrl || ''
  }

  return url
}

// Helper function for generating common metadata
const getCommonMetadata = (
  doc: Partial<Post> | Partial<Medicine>,
  fallbackTitle: string,
  fallbackSlug: string,
) => {
  const ogImage = getImageURL(doc?.meta?.image || doc?.heroImage)

  return {
    description: doc?.meta?.description || '',
    openGraph: mergeOpenGraph({
      description: doc?.meta?.description || '',
      images: ogImage
        ? [
            {
              url: ogImage,
            },
          ]
        : undefined,
      title: doc?.meta?.title || fallbackTitle,
      url: fallbackSlug,
    }),
  }
}

// Generate metadata for Post
export const generateMetaPost = async (doc: Partial<Post>): Promise<Metadata> => {
  const title = doc?.meta?.title || doc.title || 'Default Post Title'
  const slug = Array.isArray(doc.slug) ? doc.slug.join('/') : '/posts/' + (doc.slug || '')
  const commonMetadata = getCommonMetadata(doc, title, slug)

  return {
    ...commonMetadata,
    title,
  }
}

// Generate metadata for Medicine
export const generateMetaMedicine = async (
  doc: Partial<Medicine | MedicineV2>,
): Promise<Metadata> => {
  const title = doc?.meta?.title || doc?.title || 'Default Medicine Title'
  const slug = Array.isArray(doc?.slug)
    ? doc.slug.join('/')
    : `/products/medicines/${doc?.slug || ''}`
  const description = doc?.meta?.description || doc?.description || 'Default Medicine Description'

  const ogImage = getImageURL(doc?.meta?.image || doc?.heroImage)

  return {
    description,
    openGraph: mergeOpenGraph({
      description,
      images: ogImage
        ? [
            {
              url: ogImage,
            },
          ]
        : undefined,
      title,
      url: slug,
    }),
    title,
  }
}

export const generateMetaProductV2 = async (doc: Partial<Product>): Promise<Metadata> => {
  const title = doc?.meta?.title || doc?.title || 'Default Product Title'
  const slug = Array.isArray(doc?.slug) ? doc.slug.join('/') : `/products-v2/${doc?.slug || ''}`
  const description = doc?.meta?.description || doc?.description || 'Default Product Description'

  const ogImage = getImageURL(doc?.meta?.image || doc?.heroImage)
  return {
    description: description || '',
    openGraph: mergeOpenGraph({
      description,
      images: ogImage
        ? [
            {
              url: ogImage,
            },
          ]
        : undefined,
      title,
      url: slug,
    }),
    title: title || '',
  }
}

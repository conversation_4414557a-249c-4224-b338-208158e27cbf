import { LocaleEnum } from '@/enums/locale.enum'
import { ProductCategory } from '@/payload-types'
import { cleanAndConvertSelectObject, SelectObject } from '@/utilities/cleanAndConvertSelectObject'
import { withErrorHandling } from '@/utils/errorHandlers'
import { PayloadRequest, Where } from 'payload'

const getFilteredProductsImpl = async (req: PayloadRequest) => {
  const queries = req.query
  const user = req?.user
  const { where, select, populate, locale, categories, ...otherQueries } = queries

  // Normalize categories param into array
  const listCategories = Array.isArray(categories) ? categories : categories ? [categories] : []

  // Fetch lft/rgt for provided categories
  const categoriesData: ProductCategory[] =
    listCategories.length > 0
      ? await Promise.allSettled(
          listCategories.map((category) =>
            req.payload.findByID({
              collection: 'product-categories',
              id: category,
              select: { lft: true, rgt: true },
            }),
          ),
        ).then((results) =>
          results
            .filter(
              (result): result is PromiseFulfilledResult<ProductCategory> =>
                result.status === 'fulfilled',
            )
            .map((result) => result.value),
        )
      : []

  // Collect all descendant category IDs in parallel
  const descendantResults =
    categoriesData.length > 0
      ? await Promise.allSettled(
          categoriesData.map((category) =>
            req.payload.find({
              collection: 'product-categories',
              where: {
                and: [
                  { lft: { greater_than_equal: category.lft } },
                  { rgt: { less_than_equal: category.rgt } },
                ],
              },
              select: { id: true, lft: true, rgt: true },
              pagination: false,
            }),
          ),
        )
      : []

  let categoryIDs: string[] = []
  for (const result of descendantResults) {
    if (result.status === 'fulfilled') {
      categoryIDs.push(...result.value.docs.map((c: ProductCategory) => c.id as string))
    }
  }

  // Remove duplicates
  categoryIDs = [...new Set(categoryIDs)]

  const { or, and, ...rest } = (where as Where) || {}
  const dynamicWhere: Where = {
    ...rest,
    ...(or && { or }),
    ...(and && { and }),
  }

  // Final where conditions
  const finalWhere: Where = {
    and: [
      { unverified: { not_equals: true } },
      { slug: { exists: true } },
      ...(categoryIDs.length > 0 ? [{ categories: { in: categoryIDs } }] : []),
      dynamicWhere,
    ],
  }

  // Query products
  const products = await req.payload.find({
    collection: 'products',
    locale: (locale as LocaleEnum) || LocaleEnum.VI,
    where: finalWhere,
    populate: populate
      ? {
          ...cleanAndConvertSelectObject(populate as SelectObject),
        }
      : undefined,
    select: select
      ? {
          ...cleanAndConvertSelectObject(select as SelectObject),
        }
      : undefined,
    ...otherQueries,
  })

  const productIds = products.docs.map((m) => m.id)

  const currentFavorites = await req.payload.find({
    collection: 'favorite-products',
    where: {
      and: [
        {
          'product.id': { in: productIds },
          'user.id': { equals: user?.id },
        },
      ],
    },
    pagination: false,
    select: { product: true },
    depth: 0,
  })

  const favorites = new Set(currentFavorites.docs.map((d) => d.product as string))

  products.docs = products.docs.map((doc) => {
    const x = {
      ...doc,
      isFavorite: favorites.has(doc.id),
    }
    return x
  })
  return Response.json(products)
}

// Wrap the handler with standardized error handling
export const getFilteredProductsHandler = withErrorHandling(
  getFilteredProductsImpl,
  'An error occurred while getting filtered products. Please try again later.',
)

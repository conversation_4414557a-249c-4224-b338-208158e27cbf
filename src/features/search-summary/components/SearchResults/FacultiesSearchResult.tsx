'use client'

import arrow from '@/assets/icons/arrow-left-primary.svg'
import crown from '@/assets/icons/crown-2.svg'
import map from '@/assets/icons/map.svg'
import padlock from '@/assets/icons/padlock.svg'
import { SearchByKeywordsRef } from '@/components/SearchByKeywords/SearchByKeywords'
import { Button } from '@/components/ui/Button/Button'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { BodyPart, Faculty, Subscription, Symptom } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import { LocalizeField } from '@/types/custom-payload.type'
import { cn } from '@/utilities/cn'
import { InfiniteData } from '@tanstack/react-query'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { PaginatedDocs } from 'payload'
import { stringify } from 'qs-esm'
import React, { useMemo, useRef, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { BaseSearchResult } from './BaseSearchResult'
import { EmptyList } from '../EmptyList/EmptyList'
import { NATIVE_APP_ROUTES } from '@/routes/nativeAppRoutes'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
const LIMIT_RESULTS = 2
interface FacultiesSearchResultProps {
  faculties: InfiniteData<PaginatedDocs<Faculty>, unknown> | undefined
  isGetFacultiesLoading: boolean
  showAllResults?: boolean
  isInfiniteScroll?: boolean
  fetchNextPage?: () => void
  hasNextPage?: boolean
  onShowMoreClick?: () => void
  showShowMoreButton?: boolean
  isFetchingNextPage?: boolean
  isShowEmptyState?: boolean
  shouldShowTotalDocs?: boolean
  isNative?: boolean
}

export const FacultiesSearchResult: React.FC<FacultiesSearchResultProps> = React.memo(
  ({
    faculties,
    isGetFacultiesLoading,
    showAllResults = false,
    isInfiniteScroll = false,
    fetchNextPage,
    hasNextPage,
    onShowMoreClick,
    showShowMoreButton = false,
    isFetchingNextPage = false,
    isShowEmptyState = true,
    shouldShowTotalDocs = true,
    isNative = false,
  }) => {
    const t = useTranslations()
    const { user, status } = useAuthentication()
    const { subscription: userSubscription } = user || {}
    const { type } = (userSubscription as Subscription) || {}
    const { secondaryLanguage } = useAppLanguage()
    const locale = useLocale()
    const searchByKeywordsRef = useRef<SearchByKeywordsRef>(null)
    if (status === 'unauthorized') return <ProtectedContentBlock></ProtectedContentBlock>

    const totalDocs = faculties?.pages[0]?.totalDocs || 0

    const shouldShowMoreButton = Boolean(
      showShowMoreButton &&
        !isGetFacultiesLoading &&
        status !== 'loading' &&
        faculties &&
        faculties.pages[0]?.docs.length > 0,
    )

    if (!isShowEmptyState && totalDocs === 0 && !isGetFacultiesLoading) return null

    return (
      <BaseSearchResult
        label={`${t('MES-564')} ${shouldShowTotalDocs ? `(${totalDocs > 99 ? '99+' : totalDocs})` : ''}`}
        onShowMoreClick={onShowMoreClick}
        showShowMoreButton={shouldShowMoreButton}
        tooltipContent={t('MES-569')}
      >
        {/* Loading state */}
        {(isGetFacultiesLoading ||
          (!faculties && !isGetFacultiesLoading) ||
          status === 'loading') && (
          <>
            {isInfiniteScroll ? (
              <>
                {!isFetchingNextPage && (
                  <div className="space-y-2">
                    {Array.from({ length: showAllResults ? 5 : LIMIT_RESULTS }, (_, index) => (
                      <div key={`skeleton-${index}`} className={cn('relative flex flex-col gap-2')}>
                        <Skeleton className="h-28 w-full"></Skeleton>
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <div className="space-y-2">
                {Array.from({ length: showAllResults ? 5 : LIMIT_RESULTS }, (_, index) => (
                  <div key={`skeleton-${index}`} className={cn('relative flex flex-col gap-2')}>
                    <Skeleton className="h-28 w-full"></Skeleton>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Empty state */}
        {faculties && faculties.pages[0]?.docs.length === 0 && <EmptyList></EmptyList>}

        {/* Regular view for summary tab */}
        {!isInfiniteScroll && faculties && faculties.pages[0]?.docs.length > 0 && (
          <div className="space-y-2">
            {faculties.pages[0]?.docs
              .slice(0, showAllResults ? faculties.pages[0]?.docs.length : LIMIT_RESULTS)
              .map((faculty) => (
                <FacultySearchCard
                  keywords={searchByKeywordsRef?.current?.getListSearchKeywords || []}
                  key={`summary-${faculty.id}`}
                  faculty={faculty}
                  locale={locale}
                  secondaryLang={secondaryLanguage}
                  userSuscriptionPlan={type}
                  isNative={isNative}
                />
              ))}
          </div>
        )}

        {/* Infinite scroll for faculties tab */}
        {isInfiniteScroll && faculties && faculties.pages[0]?.docs.length > 0 && (
          <InfiniteScroll
            dataLength={faculties.pages.length}
            next={() => {
              if (hasNextPage) {
                fetchNextPage?.()
              }
            }}
            hasMore={!!hasNextPage}
            loader={
              <div className="flex items-center justify-center py-4">
                <Spinner></Spinner>
              </div>
            }
            scrollThreshold={0.8}
            className="space-y-2"
          >
            {faculties.pages.map((group, pageIndex) => (
              <React.Fragment key={`page-${pageIndex}`}>
                {group.docs.map((faculty) => (
                  <FacultySearchCard
                    keywords={searchByKeywordsRef?.current?.getListSearchKeywords || []}
                    key={`infinite-${faculty.id}-${pageIndex}`}
                    faculty={faculty}
                    locale={locale}
                    secondaryLang={secondaryLanguage}
                    userSuscriptionPlan={type}
                    isNative={isNative}
                  />
                ))}
              </React.Fragment>
            ))}
          </InfiniteScroll>
        )}
      </BaseSearchResult>
    )
  },
)

FacultiesSearchResult.displayName = 'FacultiesSearchResult'

// FacultySearchCard
interface FacultySearchCardProps {
  faculty: Faculty
  locale: string
  secondaryLang: string
  keywords: string[]
  userSuscriptionPlan: Subscription['type']
  isNative?: boolean
}

const FacultySearchCard: React.FC<FacultySearchCardProps> = ({
  faculty,
  locale,
  secondaryLang,
  keywords,
  userSuscriptionPlan,
  isNative,
}) => {
  const { name, id, bodyParts, symptoms, subscription: availableAt } = faculty
  const localizedName = name as unknown as LocalizeField<string>
  const ableToView = availableAt?.some((v) => (v as Subscription)?.type === userSuscriptionPlan)
  const generateGoogleMapQuery = (name: string) => {
    return stringify({ query: name })
  }
  const t = useTranslations()
  const { redirectToScreen } = useWebViewMessaging()
  return (
    <div
      className={cn(
        'border-dvider rounded-lg border bg-white p-4',
        !ableToView &&
          'pointer-events-none border border-divider bg-custom-background-disable opacity-60',
      )}
    >
      {/* Link to Medical Handbook */}
      <div
        className={cn('flex items-center justify-between gap-x-1')}
        onClick={(e) => {
          if (!ableToView) {
            e.preventDefault()
          }
        }}
      >
        <div className="typo-link-3 line-clamp-1 text-primary">
          {localizedName?.[locale]}{' '}
          {secondaryLang && localizedName?.[secondaryLang] && (
            <span className="text-ellipsis">{` (${localizedName?.[secondaryLang]})`}</span>
          )}
        </div>
        {!ableToView && (
          <Image src={crown} alt="crown" width={18} height={18} className="size-[18px] shrink-0" />
        )}
      </div>

      <div className="mt-3 flex flex-col gap-y-3">
        <div className="space-y-2">
          {/* Display Body Part Name */}
          <div className="typo-body-6">
            {bodyParts?.map((part, index) => {
              const { name, id } = part as BodyPart
              const localizedBodyPartName = name as unknown as LocalizeField<string>
              return (
                <span key={`bodypart-${id}-${index}`}>
                  {index !== 0 && ', '}
                  {localizedBodyPartName?.[locale]}{' '}
                  {secondaryLang && localizedBodyPartName?.[secondaryLang] && (
                    <span className="text-ellipsis">{` (${localizedBodyPartName?.[secondaryLang]})`}</span>
                  )}
                </span>
              )
            })}
          </div>

          {/* Display Symptoms */}
          <SymptomsList
            keywords={keywords}
            symptoms={symptoms as Symptom[]}
            locale={locale}
            secondaryLang={secondaryLang}
            ableToView={ableToView}
          />
        </div>
      </div>
      <a
        href={`https://www.google.com/maps/search/?api=1&${generateGoogleMapQuery(localizedName?.['ja'])}`}
        target="_blank"
        className="typo-link-3 ml-auto mt-3 flex w-fit items-center gap-x-2 text-custom-green-600"
      >
        {t('MES-131')}
        <Image src={map} alt="map" width={18} height={18} className="size-[18px]" />
      </a>
      <Link
        href={`/medical-handbook/faculties/${id}`}
        className="typo-link-3 ml-auto mt-2 flex items-center justify-end gap-x-2 text-primary"
        onClick={(e) => {
          if (isNative) {
            e.preventDefault()
            redirectToScreen(NATIVE_APP_ROUTES.MEDICAL_HANDBOOK_FACULTIES.path + `/${id}`)
            return
          }
        }}
      >
        {t('MES-226')}
        <Image src={arrow} alt="arrow" width={18} height={18} className="size-[18px] rotate-180" />
      </Link>
    </div>
  )
}

// SymptomsList
interface SymptomsListProps {
  symptoms: Symptom[]
  locale: string
  secondaryLang?: string
  keywords: string[]
  ableToView?: boolean
}

const SymptomsList: React.FC<SymptomsListProps> = ({
  symptoms,
  locale,
  secondaryLang,
  keywords,
  ableToView,
}) => {
  const [showAllSymptoms, setShowAllSymptoms] = useState(false)
  const symptomLimit = 2
  // Sort symptoms based on keyword match
  const sortedSymptoms = useMemo(() => {
    return [...symptoms].sort((a, b) => {
      const aName = (a.name as unknown as LocalizeField<string>)[locale] || ''
      const bName = (b.name as unknown as LocalizeField<string>)[locale] || ''

      const aMatch = keywords.some((keyword) => aName.includes(keyword))
      const bMatch = keywords.some((keyword) => bName.includes(keyword))

      // Prioritize symptoms that match the keywords
      if (aMatch && !bMatch) return -1
      if (!aMatch && bMatch) return 1
      return 0
    })
  }, [symptoms, keywords, locale])
  const displayedSymptoms = showAllSymptoms ? sortedSymptoms : sortedSymptoms.slice(0, symptomLimit)
  const t = useTranslations()

  return (
    <div className="space-y-2">
      <ul className="!list-disc space-y-2">
        {displayedSymptoms.map((symptom) => {
          const { id, name } = symptom as Symptom
          const localizedSymptomName = name as unknown as LocalizeField<string>
          return (
            <li key={`symptom-${id}`} className="typo-body-6 ml-6">
              {localizedSymptomName?.[locale]}
              {secondaryLang && localizedSymptomName?.[secondaryLang] && (
                <span className="text-ellipsis">{` (${localizedSymptomName?.[secondaryLang]})`}</span>
              )}
            </li>
          )
        })}
      </ul>
      {ableToView && symptoms.length > symptomLimit && (
        <div
          onClick={() => setShowAllSymptoms((prev) => !prev)}
          className="typo-link-3 flex cursor-pointer items-center gap-x-1 pl-6 text-primary"
        >
          {t('MES-22')}
          <Image
            src={arrow}
            alt="arrow"
            width={18}
            height={18}
            className={cn('size-[18px]', showAllSymptoms ? 'rotate-90' : '-rotate-90')}
          />
        </div>
      )}
    </div>
  )
}

// ProtectedContentBlock
const ProtectedContentBlock = () => {
  const t = useTranslations()
  return (
    <div className="relative">
      <BaseSearchResult label={`${t('MES-564')} (0)`} showShowMoreButton={false}>
        <div className="space-y-2">
          <div className="space-y-2 rounded-[6px] border border-divider p-3">
            <div className="h-3 w-3/4 bg-custom-neutral-100"></div>
            <div className="h-3 w-2/3 bg-custom-neutral-100"></div>
            <div className="h-3 w-1/3 bg-custom-neutral-100"></div>
          </div>
          <div className="space-y-2 rounded-[6px] border border-divider p-3">
            <div className="h-3 w-3/4 bg-custom-neutral-100"></div>
            <div className="h-3 w-2/3 bg-custom-neutral-100"></div>
            <div className="h-3 w-1/3 bg-custom-neutral-100"></div>
          </div>
        </div>
      </BaseSearchResult>
      <div className="absolute inset-0 flex items-center justify-center backdrop-blur-[2px]">
        <div className="flex flex-col items-center gap-y-2">
          <Image src={padlock} alt="padlock" width={30} height={30} />
          <div className="typo-body-3 text-center">{t('MES-571')}</div>
          <div className="typo-body-7 max-w-[80%] text-center">{t('MES-572')}</div>
          <Button size="sm" className="w-fit" asChild>
            <Link href={APP_ROUTES.LOGIN.path}>{t('MES-06')}</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

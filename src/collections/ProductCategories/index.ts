import { admins } from '@/access/admins'
import { anyone } from '@/access/anyone'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product-v2/constants'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { slugField } from '@/fields/slug'
import type { CollectionConfig } from 'payload'
import { getNestedProductCategoriesHandler } from './handlers/getNestedProductCategories'
import {
  calculateNestedCategoriesAfterChange,
  calculateNestedCategoriesAfterDelete,
} from './hooks/calculateNestedCategoriesAfterChange'

export const ProductCategories: CollectionConfig = {
  slug: 'product-categories',
  labels: {
    plural: 'Categories',
  },
  access: {
    create: admins,
    delete: admins,
    read: anyone,
    update: admins,
  },
  admin: {
    group: 'Products',
    useAsTitle: 'title',
  },
  defaultPopulate: {
    title: true,
    slug: true,
    icon: true,
    parent: true,
    lft: true,
    rgt: true,
    categoryLevel: true,
    type: true,
  },
  hooks: {
    afterChange: [calculateNestedCategoriesAfterChange],
    afterDelete: [calculateNestedCategoriesAfterDelete],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      localized: true,
      index: true,
    },
    {
      name: 'icon',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'parent',
      type: 'relationship',
      relationTo: 'product-categories',
      label: 'Parent Category',
      required: false,
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ id, data }) => {
        const productType = data.type
        if (!productType) return false
        // Case 1: Remove itself - prevent self-parenting
        const filters: any = {
          id: {
            not_equals: id,
          },
          type: {
            equals: productType,
          },
        }

        // Case 2: Remove descendants (lower categories) - prevent circular references
        // If editing existing category with lft/rgt values
        if (data?.lft && data?.rgt) {
          filters.and = [
            {
              or: [
                // Include categories that are NOT descendants
                { lft: { less_than: data.lft } },
                { rgt: { greater_than: data.rgt } },
                // Include categories where lft/rgt is null (new categories)
                { lft: { exists: false } },
                { rgt: { exists: false } },
              ],
            },
            {
              type: {
                equals: productType,
              },
            },
          ]
        }

        return filters
      },
    },
    {
      name: 'lft',
      type: 'number',
      admin: {
        readOnly: true,
        position: 'sidebar',
        description: 'Left boundary for nested set model (auto-calculated)',
      },
      defaultValue: 0,
      index: true,
    },
    {
      name: 'rgt',
      type: 'number',
      admin: {
        readOnly: true,
        position: 'sidebar',
        description: 'Right boundary for nested set model (auto-calculated)',
      },
      defaultValue: 0,
      index: true,
    },
    {
      name: 'categoryLevel',
      type: 'number',
      admin: {
        readOnly: true,
        position: 'sidebar',
        description: 'Category level (auto-calculated)',
      },
      defaultValue: 0,
      index: true,
    },
    {
      name: 'type',
      type: 'select',
      options: Object.values(PRODUCT_V2_TYPE_OPTIONS).map((option) => ({
        label: option.label,
        value: option.value,
      })),
      required: true,
      defaultValue: ProductV2TypeEnum.MEDICINE,
    },
    ...slugField(),
  ],
  endpoints: [
    {
      path: '/nested-categories/:id',
      method: 'get',
      handler: getNestedProductCategoriesHandler,
    },
  ],
}

'use client'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { useTranslations } from 'next-intl'
import { useSidebar } from '@/contexts/Sidebar/SidebarContext'
import { ChevronDownIcon } from 'lucide-react'
import menuIcon from '@/assets/icons/menu-white.svg'
import { Button } from '../ui/Button/Button'

// Image
import homeIcon from '@/assets/icons/home-white.svg'
import dictionaryIcon from '@/assets/icons/book-white.svg'
import bookHandIcon from '@/assets/icons/insurance-hands-white.svg'
import medicineIcon from '@/assets/icons/drugs-pill-white.svg'
import newsIcon from '@/assets/icons/document-white.svg'
import examinationIcon from '@/assets/icons/medical-examination-icon-white.svg'

import logoutIcon from '@/assets/icons/logout-white.svg'
import logoWap from '@/assets/icons/logo-wap.svg'
import exportIcon from '@/assets/icons/export.svg'

type NavItem = {
  name: string
  urlIcon?: string
  path?: string
  subItems?: { name: string; path: string; pro?: boolean; new?: boolean }[]
}

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered, toggleSidebar } = useSidebar()
  const pathname = usePathname()

  const t = useTranslations()

  const navItems: NavItem[] = useMemo(
    () => [
      {
        name: t('MES-37'),
        path: '/desktop/home',
        urlIcon: homeIcon,
      },
      {
        name: t('MES-577'),
        path: '/desktop/dictionary',
        urlIcon: dictionaryIcon,
      },
      {
        name: t('MES-33'),
        path: '/desktop/medical-notebook',
        urlIcon: bookHandIcon,
      },
      {
        name: t('MES-561'),
        urlIcon: medicineIcon,
        path: '/desktop/medicines',
        // subItems: [
        //   {
        //     name: t('MES-721'),
        //     path: '/desktop/medicines/drugs-cosmetics',
        //   },
        //   {
        //     name: t('MES-471'),
        //     path: '/functional-food',
        //   },
        //   {
        //     name: t('MES-722'),
        //     path: '/medical-product',
        //   },
        // ],
      },
      {
        name: t('MES-19'),
        path: '/desktop/news',
        urlIcon: newsIcon,
      },
      {
        name: t('MES-720'),
        path: '/desktop/medical-form',
        urlIcon: examinationIcon,
      },
    ],
    [t],
  )

  const [openSubmenus, setOpenSubmenus] = useState<Set<string>>(new Set())
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>({})
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({})

  // const isActive = (path: string) => path === pathname;
  const isActive = useCallback((path: string) => pathname.includes(path), [pathname])

  const checkSubItemActive = useCallback(
    (navItem: NavItem[]) => {
      return navItem.some((item) => item.path && isActive(item.path))
    },
    [isActive],
  )
  useEffect(() => {
    // Check if the current path matches any submenu item
    const matchedSubmenus = new Set<string>()
    ;['main', 'others'].forEach((menuType) => {
      const items = menuType === 'main' ? navItems : []
      items.forEach((nav, index) => {
        if (nav.subItems) {
          nav.subItems.forEach((subItem) => {
            if (isActive(subItem.path)) {
              const key = `${menuType}-${index}`
              matchedSubmenus.add(key)
            }
          })
        }
      })
    })

    // Update open submenus to include matched ones
    setOpenSubmenus((prevOpenSubmenus) => {
      const newOpenSubmenus = new Set(prevOpenSubmenus)
      matchedSubmenus.forEach((key) => newOpenSubmenus.add(key))
      return newOpenSubmenus
    })
  }, [pathname, isActive, navItems])

  useEffect(() => {
    // Set the height of the submenu items when submenus are opened
    openSubmenus.forEach((key) => {
      if (subMenuRefs.current[key]) {
        setSubMenuHeight((prevHeights) => ({
          ...prevHeights,
          [key]: subMenuRefs.current[key]?.scrollHeight || 0,
        }))
      }
    })
  }, [openSubmenus])

  const handleSubmenuToggle = (index: number, menuType: 'main' | 'others') => {
    const key = `${menuType}-${index}`
    setOpenSubmenus((prevOpenSubmenus) => {
      const newOpenSubmenus = new Set(prevOpenSubmenus)
      if (newOpenSubmenus.has(key)) {
        newOpenSubmenus.delete(key)
      } else {
        newOpenSubmenus.add(key)
      }
      return newOpenSubmenus
    })
  }

  const handleClickShowSideBar = () => {
    toggleSidebar()
  }

  const renderMenuItems = (navItems: NavItem[], menuType: 'main' | 'others') => (
    <ul
      key={menuType}
      className={`flex flex-col ${!(isExpanded || isHovered || isMobileOpen) && 'items-center justify-center'} gap-4 text-white`}
    >
      {navItems.map((nav, index) => (
        // <PermissionGuard permission={nav.permission} key={nav.name}>
        <React.Fragment key={index}>
          <li className="menu-item-box">
            {nav.subItems ? (
              <button
                type="button"
                onClick={() => handleSubmenuToggle(index, menuType)}
                className={`menu-item group flex items-center gap-2 ${
                  openSubmenus.has(`${menuType}-${index}`) ? 'menu-item-open' : 'menu-item-close'
                } cursor-pointer ${
                  !isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'
                } ${checkSubItemActive(nav.subItems) ? 'menu-sub-item-active' : 'menu-sub-item-inactive'}`}
              >
                <span
                  className={`size-5 ${
                    openSubmenus.has(`${menuType}-${index}`)
                      ? 'menu-item-icon-open'
                      : 'menu-item-icon-close'
                  }`}
                >
                  {nav.urlIcon && (
                    <Image
                      className="inline-block"
                      src={nav.urlIcon}
                      alt="Logo"
                      width={24}
                      height={24}
                    />
                  )}
                </span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <span className="menu-item-text">{nav.name}</span>
                )}
                {(isExpanded || isHovered || isMobileOpen) && (
                  <ChevronDownIcon
                    className={`ml-auto size-5 transition-transform duration-200 ${
                      openSubmenus.has(`${menuType}-${index}`) ? 'text-brand-500 rotate-180' : ''
                    }`}
                  />
                )}
              </button>
            ) : (
              nav.path && (
                <Link
                  href={nav.path}
                  className={`menu-item group flex items-center gap-2 ${
                    isActive(nav.path) ? 'menu-item-active' : 'menu-item-inactive'
                  }`}
                >
                  <span
                    className={`size-6 ${
                      isActive(nav.path) ? 'menu-item-icon-active' : 'menu-item-icon-inactive'
                    }`}
                  >
                    {nav.urlIcon && (
                      <Image
                        className="inline-block"
                        src={nav.urlIcon}
                        alt="Logo"
                        height={24}
                        width={24}
                      />
                    )}
                  </span>
                  {(isExpanded || isHovered || isMobileOpen) && (
                    <span className="menu-item-text">{nav.name}</span>
                  )}
                </Link>
              )
            )}
            {nav.subItems && (isExpanded || isHovered || isMobileOpen) && (
              <div
                ref={(el) => {
                  subMenuRefs.current[`${menuType}-${index}`] = el
                }}
                className="overflow-hidden transition-all duration-300"
                style={{
                  height: openSubmenus.has(`${menuType}-${index}`)
                    ? `${subMenuHeight[`${menuType}-${index}`]}px`
                    : '0px',
                }}
              >
                <ul className="ml-9 mt-2 flex flex-col gap-3 space-y-1">
                  {nav.subItems.map((subItem) => (
                    <li key={subItem.name}>
                      <Link
                        href={subItem.path}
                        className={`menu-dropdown-item ${
                          isActive(subItem.path)
                            ? 'menu-dropdown-item-active'
                            : 'menu-dropdown-item-inactive'
                        }`}
                      >
                        {subItem.name}
                        {/* <span className="ml-auto flex items-center gap-1">
                          {subItem.new && (
                            <span
                              className={`ml-auto ${
                                isActive(subItem.path)
                                  ? 'menu-dropdown-badge-active'
                                  : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge`}
                            >
                              new
                            </span>
                          )}
                          {subItem.pro && (
                            <span
                              className={`ml-auto ${
                                isActive(subItem.path)
                                  ? 'menu-dropdown-badge-active'
                                  : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge`}
                            >
                              pro
                            </span>
                          )}
                        </span> */}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </li>
        </React.Fragment>
      ))}
    </ul>
  )

  return (
    <aside
      className={`fixed left-0 top-0 z-50 mt-16 flex h-screen flex-col justify-between border-r border-border bg-[linear-gradient(to_bottom,#1157C8,#3D8DEF,#639BF4)] px-5 text-foreground transition-all duration-300 ease-in-out lg:mt-0 ${
        isExpanded || isMobileOpen ? 'w-[300px]' : isHovered ? 'w-[300px]' : 'w-[90px]'
      } ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex flex-1 flex-col">
        <div
          className={`flex py-8 ${!isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'}`}
        >
          <Button className="p-0" onClick={handleClickShowSideBar}>
            {isExpanded || isHovered || isMobileOpen ? (
              <div className="flex items-center rounded-lg bg-[rgba(191,237,254,0.15)] p-2">
                <Image className="inline-block" src={menuIcon} alt="Logo" width={24} height={24} />
              </div>
            ) : (
              <div className="flex items-center rounded-lg bg-[rgba(191,237,254,0.15)] p-2">
                <Image className="inline-block" src={menuIcon} alt="Logo" width={24} height={24} />
              </div>
            )}
          </Button>
        </div>
        <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
          <nav className="mb-6">
            <div className="flex flex-col gap-4">
              <div>
                {/* <h2
                  className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                    ? 'lg:justify-center'
                    : 'justify-start'
                  }`}
                >
                  {isExpanded || isHovered || isMobileOpen
                    ? (
                        'Menu'
                      )
                    : (
                        <HorizontaLDots />
                      )}
                </h2> */}
                {renderMenuItems(navItems, 'main')}
              </div>

              {/* <div className="">
                <h2
                  className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                    ? 'lg:justify-center'
                    : 'justify-start'
                  }`}
                >
                  {isExpanded || isHovered || isMobileOpen
                    ? (
                        'Others'
                      )
                    : (
                        <HorizontaLDots />
                      )}
                </h2>
                {renderMenuItems(othersItems, 'others')}
              </div> */}
            </div>
          </nav>
        </div>
      </div>

      <div
        className={`flex flex-col gap-3 pb-6 ${!(isExpanded || isHovered || isMobileOpen) && 'items-center justify-center'}`}
      >
        {/* divide */}
        {(isExpanded || isHovered || isMobileOpen) && (
          <div className="mb-7 h-[1px] w-full bg-neutral-300"></div>
        )}
        {/* button log out  */}
        <div className="inline-flex cursor-pointer items-center gap-2 text-white">
          <Image alt="logout-icon" src={logoutIcon} width={24} height={24} />
          {(isExpanded || isHovered || isMobileOpen) && <span>{t('MES-57')}</span>}
        </div>

        {/* link */}

        {(isExpanded || isHovered || isMobileOpen) && (
          <Link href="" className="rounded-lg bg-[rgba(63,67,71,0.15)] px-3 py-2 text-white">
            <Image className="mb-2" alt="logo-wap" src={logoWap} width={30} height={20} />
            <p>{t('MES-630')}</p>
            <div className="flex items-center justify-between gap-2">
              <p>{t('MES-631')}</p>
              <Image alt="icon-link" src={exportIcon} height={16} width={16} />
            </div>
          </Link>
        )}
      </div>
    </aside>
  )
}

export default AppSidebar

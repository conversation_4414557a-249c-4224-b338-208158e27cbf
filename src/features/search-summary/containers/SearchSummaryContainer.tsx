'use client'

import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { MultiSelectV2PropssRef } from '@/components/ui/Select/MultiSelectV2'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { HomeSearchKeywordBox } from '@/features/home/<USER>/HomeSearch/HomeSearchKeywordBox'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetExplainKeywords } from '@/hooks/query/ai-bot/useGetExplainKeywords'
import { useGetInfiniteFaculties } from '@/hooks/query/faculty/useGetInfiniteFaculties'
import { useCheckKeywordExist } from '@/hooks/query/keyword/useCheckKeywordExist'
import { useGetInfiniteKeywords } from '@/hooks/query/keyword/useGetInfiniteKeywords'
import { useGetInfiniteMedicines } from '@/hooks/query/medicine/useGetInfiniteMedicines'
import { APP_ROUTES } from '@/routes'
import { cn } from '@/utilities/cn'
import { processQueryTerms } from '@/utilities/processQueryTerms'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { parse } from 'qs-esm'
import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { EmptyList } from '../components/EmptyList/EmptyList'
import {
  KeywordsExplanation,
  KeywordsExplanationAccess,
} from '../components/KeywordsExplanation/KeywordsExplanation'
import { PopulatedSearchKeywords } from '../components/PopulatedSearchKeywords/PopulatedSearchKeywords'
import { RelatedKeywords } from '../components/RelatedKeywords/RelatedKeywords'
import { FacultiesSearchResult } from '../components/SearchResults/FacultiesSearchResult'
import { KeywordsSearchResult } from '../components/SearchResults/KeywordsSearchResult'
import { ProductsSearchResult } from '../components/SearchResults/ProductsSearchResult'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import { useGetInfiniteProductsV2 } from '@/features/product-v2/hooks/query/useGetInfiniteProductsV2'
import { ProductsV2SearchResult } from '../components/SearchResults/ProductsV2SearchResult'

const TABS = {
  summary: {
    value: 'summary',
    label: 'MES-141',
  },
  bot: {
    value: 'HICO-BOT',
    label: 'MES-639',
  },
  faculties: {
    value: 'faculties',
    label: 'MES-564',
  },
  keywords: {
    value: 'keywords',
    label: 'MES-565',
  },
  products: {
    value: 'products',
    label: 'MES-567',
  },
}

// SearchSummaryContainer
interface SearchSummaryContainerProps {
  searchParams: {
    [key: string]: string | undefined
  }
  isNative?: boolean
}

// FACULTIES SEARCH

const FACULTIES_SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'symptoms.name', isLocalized: true },
  { path: 'name', isLocalized: true },
  { path: 'bodyParts.name', isLocalized: true },
  // Uncomment if keywords search is needed
  { path: 'questions.question.keywords.name', isLocalized: true },
]

// KEYWORDS SEARCH

const KEYWORDS_SEARCHABLE_FIELDS: FilterField[] = [{ path: 'name', isLocalized: true }]
// const KEYWORDS_CATEGORIES_FILTER = [
//   KeywordCategoryEnum.BODY_PART,
//   KeywordCategoryEnum.MEDICINE,
//   KeywordCategoryEnum.SYMPTOM_OR_DISEASE,
//   KeywordCategoryEnum.TEST_VACCINE_TREATMENT,
//   KeywordCategoryEnum.LIFESTYLE,
//   KeywordCategoryEnum.MEDICAL_SCIENCE,
//   KeywordCategoryEnum.ALLERGY_TYPE,
//   KeywordCategoryEnum.MEDICINE_TYPE,
// ]

// PRODUCTS SEARCH

const PRODUCTS_SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title', isLocalized: true },
  { path: 'keywords.name', isLocalized: true, type: 'equals' },
  { path: 'title', isLocalized: false },
  { path: 'description', isLocalized: false },
]

const PRODUCTS_V2_SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title', isLocalized: true },
  { path: 'keywords.name', isLocalized: true, type: 'equals' },
  { path: 'title', isLocalized: false },
  { path: 'description', isLocalized: false },
]

export const SearchSummaryContainer: React.FC<SearchSummaryContainerProps> = ({
  searchParams,
  isNative: isNativeView,
}) => {
  const t = useTranslations()
  const { isNative, isDetecting } = useDetectNativeEnvironment()
  const { primaryLanguage } = useAppLanguage()
  const { user } = useAuthentication()
  const multiSelectRef = useRef<MultiSelectV2PropssRef>(null)
  const searchSelectOpenRef = useRef<boolean>(false)
  // TABS
  const tabsListRef = useRef<HTMLDivElement>(null)
  const tabRefs = useRef<Record<string, HTMLButtonElement | null>>({})
  const [activeTab, setActiveTab] = React.useState(TABS.summary.value)
  // FAVORITE KEYWORDS
  const favoriteKeywords = useRef<Set<string>>(new Set())
  // NEW DATA INDICATOR
  const [hasNewBotData, setHasNewBotData] = React.useState(false)
  const [hasViewedBotTab, setHasViewedBotTab] = React.useState(false)

  // ROUTER
  const router = useRouter()

  // QUERY
  const { q: query } = parse(searchParams as unknown as string)

  const splitQuery = useMemo(() => {
    return Array.isArray(query) ? query.map((item) => String(item)) : query ? [String(query)] : []
  }, [query])

  const processedQueryTerms = useMemo(
    () => processQueryTerms(splitQuery),
    //eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(splitQuery)],
  )

  const primaryLangProcessedQueryTerms = useMemo(
    () => processQueryTerms(splitQuery, false),
    //eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(splitQuery)],
  )

  // FACULTIES
  const { queryFilters: facultyFilters, generateQueryString } = useGenerateSearchQueryFilter({
    query: processedQueryTerms,
    searchAbleFields: FACULTIES_SEARCHABLE_FIELDS,
  })

  const {
    faculties,
    isGetFacultiesLoading,
    fetchNextPage: fetchNextPageFaculties,
    hasNextPage: hasNextPageFaculties,
    isFetchingNextPage: isFetchingNextPageFaculties,
  } = useGetInfiniteFaculties({
    params: {
      depth: 1,
      limit: 15,
      locale: 'all',
      fallbackLocale: false,
      where: {
        and: [
          {
            or: facultyFilters,
          },
        ],
      },
    },
    config: {
      enabled: !!user && (activeTab === TABS.faculties.value || activeTab === TABS.summary.value),
    },
  })

  // KEYWORDS
  const { queryFilters: keywordFilters } = useGenerateSearchQueryFilter({
    query: processedQueryTerms,
    searchAbleFields: KEYWORDS_SEARCHABLE_FIELDS,
  })
  const keywordParams = useMemo(
    () => ({
      locale: 'all',
      where: {
        and: [
          {
            or: keywordFilters,
          },
        ],
      },
      limit: 15,
      depth: 5,
      withFavoriteFlag: true,
      select: {
        id: true,
        name: true,
        hiragana: true,
        audio: true,
        relatedImages: true,
        description: true,
      },
    }),
    //eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(keywordFilters)],
  )

  const {
    keywords,
    isGetKeywordsLoading,
    fetchNextPage: fetchNextPageKeywords,
    hasNextPage: hasNextPageKeywords,
    isFetchingNextPage: isFetchingNextPageKeywords,
  } = useGetInfiniteKeywords({
    params: keywordParams,
    config: {
      enabled: activeTab === TABS.keywords.value || activeTab === TABS.summary.value,
    },
  })

  const relatedKeywords = useMemo(() => {
    if (splitQuery.length === 0) {
      return []
    }
    return (
      keywords?.pages[0]?.docs
        ?.slice(0, 5)
        .filter(
          (keyword) =>
            keyword.name[primaryLanguage]?.toLowerCase() !==
            primaryLangProcessedQueryTerms?.[0]?.toLowerCase(),
        )
        .map((keyword) => keyword.name[primaryLanguage]) ?? []
    )
  }, [keywords, primaryLanguage, primaryLangProcessedQueryTerms, splitQuery])

  // Check keyword exist
  const { checkKeywordExist, isCheckKeywordExistLoading } = useCheckKeywordExist({
    params: {
      keyword: splitQuery?.[0],
    },
    useQueryOptions: {
      enabled: Boolean(
        splitQuery?.[0] && (activeTab === TABS.keywords.value || activeTab === TABS.summary.value),
      ),
      staleTime: 5 * 60 * 1000,
    },
  })

  // KEYWORDS EXPLANATION
  const isEnableExplainKeywords = useMemo(
    () =>
      Boolean(
        user &&
          checkKeywordExist?.exist === false &&
          !isCheckKeywordExistLoading &&
          !isGetKeywordsLoading,
      ),
    [checkKeywordExist?.exist, isCheckKeywordExistLoading, isGetKeywordsLoading, user],
  )

  const { explainKeywords, isGetExplainKeywordsLoading } = useGetExplainKeywords({
    params: {
      keywords: primaryLangProcessedQueryTerms,
      targetLanguage: primaryLanguage,
    },
    useQueryOptions: {
      enabled: isEnableExplainKeywords,
      staleTime: 60000,
    },
  })

  // Set new data indicator when explanations are loaded and bot tab is not active
  useEffect(() => {
    if (
      explainKeywords?.explainations &&
      explainKeywords.explainations.length > 0 &&
      !isGetExplainKeywordsLoading &&
      !hasViewedBotTab
    ) {
      setHasNewBotData(true)
    }
  }, [explainKeywords?.explainations, isGetExplainKeywordsLoading, hasViewedBotTab])

  // Reset viewed state when search query changes
  useEffect(() => {
    setHasViewedBotTab(false)
    setHasNewBotData(false)
  }, [processedQueryTerms])

  // PRODUCTS
  const { queryFilters: productsFilters } = useGenerateSearchQueryFilter({
    query: processedQueryTerms,
    searchAbleFields: PRODUCTS_SEARCHABLE_FIELDS,
  })
  const {
    medicines,
    isGetMedicinesLoading,
    fetchNextPage: fetchNextPageMedicines,
    hasNextPage: hasNextPageMedicines,
    isFetchingNextPage: isFetchingNextPageMedicines,
  } = useGetInfiniteMedicines({
    params: {
      where: {
        and: [
          {
            or: productsFilters,
          },
        ],
      },
      limit: 15,
      depth: 5,
      locale: 'all',
      draft: false,
      select: {
        type: true,
        categories: true,
        patientGroup: true,
        featured: true,
        isDietarySupplement: true,
      },
    },
    config: {
      enabled:
        (activeTab === TABS.products.value || activeTab === TABS.summary.value) &&
        !isNative &&
        !isDetecting &&
        !isNativeView,
    },
  })

  // PRODUCTS V2
  const { queryFilters: productsV2Filters } = useGenerateSearchQueryFilter({
    query: processedQueryTerms,
    searchAbleFields: PRODUCTS_V2_SEARCHABLE_FIELDS,
  })
  const {
    productsV2,
    isGetProductsV2Loading,
    fetchNextPage: fetchNextPageProductsV2,
    hasNextPage: hasNextPageProductsV2,
    isFetchingNextPage: isFetchingNextPageProductsV2,
  } = useGetInfiniteProductsV2({
    params: {
      where: {
        and: [
          {
            or: productsV2Filters,
          },
        ],
      },
      limit: 15,
      depth: 5,
      locale: 'all',
      draft: false,
      select: {
        type: true,
        categories: true,
        title: true,
        heroImage: true,
        slug: true,
        id: true,
      },
    },
    config: {
      enabled:
        (activeTab === TABS.products.value || activeTab === TABS.summary.value) &&
        isNative &&
        !isDetecting &&
        isNativeView,
    },
  })

  // Updates the URL with the new search query and resets the page to 1
  const handleListSearchKeywordsChange = (keywords: string[]) => {
    multiSelectRef.current?.close()
    const queryString = generateQueryString(keywords)
    router.push(`${APP_ROUTES.SEARCH_SUMMARY.path}${queryString ? `?${queryString}` : ''}`)
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)

    // Mark bot tab as viewed when clicked
    if (value === TABS.bot.value) {
      setHasViewedBotTab(true)
      setHasNewBotData(false)
    }

    // Scroll to top immediately
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // Also try scrolling the container if window scrolling doesn't work
    const container = document.querySelector('.select-none.px-4.py-3')
    if (container) {
      container.scrollTop = 0
    }

    // Scroll the active tab
    setTimeout(() => {
      if (tabRefs.current[value] && tabsListRef.current) {
        const tabElement = tabRefs.current[value]
        const tabsList = tabsListRef.current

        if (tabElement) {
          const tabRect = tabElement.getBoundingClientRect()
          const tabsListRect = tabsList.getBoundingClientRect()

          const scrollLeft = tabElement.offsetLeft - tabsListRect.width / 2 + tabRect.width / 2

          tabsList.scrollTo({
            left: scrollLeft,
            behavior: 'smooth',
          })
        }
      }
    }, 0)
  }

  const getFormattedTotalCount = useCallback(
    (tab = activeTab, useCap = true) => {
      let totalCount = 0

      switch (tab) {
        case TABS.summary.value:
          totalCount =
            (faculties?.pages[0]?.totalDocs || 0) +
            (keywords?.pages[0]?.totalDocs || 0) +
            (isNative || isNativeView
              ? productsV2?.pages[0]?.totalDocs || 0
              : medicines?.pages[0]?.totalDocs || 0)
          break
        case TABS.faculties.value:
          totalCount = faculties?.pages[0]?.totalDocs || 0
          break
        case TABS.keywords.value:
          totalCount = keywords?.pages[0]?.totalDocs || 0
          break
        case TABS.products.value:
          if (isNative || isNativeView) {
            totalCount = productsV2?.pages[0]?.totalDocs || 0
          } else {
            totalCount = medicines?.pages[0]?.totalDocs || 0
          }
          break
        default:
          return 0
      }

      return useCap && totalCount > 99 ? '99+' : totalCount
    },
    [activeTab, faculties, keywords, medicines, productsV2, isNative, isNativeView],
  )

  const totalSearchCount = useMemo(() => {
    const totalCount = Object.values(TABS).reduce((acc, tab) => {
      if (tab.value === TABS.summary.value) {
        return acc
      }
      const totalCount = getFormattedTotalCount(tab.value, false)

      return acc + Number(totalCount)
    }, 0)
    return totalCount > 99 ? '99+' : totalCount
  }, [getFormattedTotalCount])

  const isEmptySeachResult = useMemo(() => {
    return (
      totalSearchCount === 0 &&
      !isGetKeywordsLoading &&
      !isGetFacultiesLoading &&
      !isGetMedicinesLoading
    )
  }, [totalSearchCount, isGetKeywordsLoading, isGetFacultiesLoading, isGetMedicinesLoading])

  // Classname
  const baseTabClassName = useMemo(() => {
    return {
      base: 'typo-body-6 h-7 rounded-[20px] !bg-primary px-3 py-1 text-center',
      active: '!bg-primary-500 !text-white',
      inactive: '!bg-custom-neutral-80 !text-subdued',
    }
  }, [])

  const onShowMoreClick = useCallback((tab: string) => {
    window.scrollTo({ top: 0, behavior: 'auto' })
    requestAnimationFrame(() => {
      handleTabChange(tab)
    })
  }, [])

  // Auto switch to summary tab if bot tab is loading
  useEffect(() => {
    if (
      Boolean(user) &&
      activeTab === TABS.bot.value &&
      (isCheckKeywordExistLoading || isGetExplainKeywordsLoading)
    ) {
      setActiveTab(TABS.summary.value)
    }
  }, [activeTab, isCheckKeywordExistLoading, isGetExplainKeywordsLoading, user])
  return (
    <div className="select-none px-4 py-3">
      {/* Total count */}
      {/* {processedQueryTerms?.length > 0 && (
        <div className="typo-heading-7 mt-2 text-primary">
          <span>
            {t('MES-71')}:{' '}
            <span className={cn(totalSearchCount === 0 && 'text-danger-600')}>
              {totalSearchCount}
            </span>
          </span>
        </div>
      )} */}

      {/* Search Box */}
      <div className="flex items-center gap-x-2">
        <PreviousPageButton fallbackURL="/" title="" isNative={isNativeView || isNative}>
          <svg
            width={24}
            height={24}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14.4995 18.6L9.06621 13.1667C8.42454 12.525 8.42454 11.475 9.06621 10.8334L14.4995 5.40002"
              stroke="#1157C8"
              strokeWidth="1.5"
              strokeMiterlimit={10}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </PreviousPageButton>
        <HomeSearchKeywordBox
          multiSelectRef={multiSelectRef}
          selectProps={{
            showBadges: false,

            placeholder: t('MES-562'),
            ref: multiSelectRef,
            onSearchInputEnter: (v) => {
              handleListSearchKeywordsChange([v])
            },
            selectTriggerClassName: 'flex-row-reverse',
            onClickSearchIcon: (v) => {
              handleListSearchKeywordsChange([v])
            },
            searchTermValue: splitQuery.length > 0 ? splitQuery[0] : undefined,
            persistSearchTerm: true,
            clearSearchAfterClose: false,
            onSingleSelect: (keyword) => {
              handleListSearchKeywordsChange([keyword])
            },
            onClose: () => {
              searchSelectOpenRef.current = false
            },
            onOpen: () => {
              searchSelectOpenRef.current = true
            },
            onClearSearch: () => {
              if (!searchSelectOpenRef.current) {
                handleListSearchKeywordsChange([])
              }
            },
          }}
          showSelectedKeywords={false}
          wrapperClassName="w-full flex-1"
        ></HomeSearchKeywordBox>
      </div>

      <RelatedKeywords
        relatedKeywords={relatedKeywords}
        onKeywordClick={(keyword) => handleListSearchKeywordsChange([keyword])}
        isLoading={isGetKeywordsLoading}
      />

      <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-4 mt-2">
        <div className="hide-scroll overflow-x-auto" ref={tabsListRef}>
          <TabsList className="gap-x-2 bg-transparent">
            {/* Summary */}
            <TabsTrigger
              value={TABS.summary.value}
              className={cn(
                baseTabClassName.base,
                activeTab === TABS.summary.value
                  ? baseTabClassName.active
                  : baseTabClassName.inactive,
              )}
              ref={(el) => {
                tabRefs.current[TABS.summary.value] = el
              }}
            >
              {t(TABS.summary.label)}
            </TabsTrigger>
            {/* HICO-BOT */}
            <TabsTrigger
              value={TABS.bot.value}
              className={cn(
                baseTabClassName.base,
                activeTab === TABS.bot.value ? baseTabClassName.active : baseTabClassName.inactive,
                Boolean(user) && hasNewBotData && !hasViewedBotTab && 'animate-glow',
              )}
              ref={(el) => {
                tabRefs.current[TABS.bot.value] = el
              }}
              disabled={
                Boolean(user) ? isCheckKeywordExistLoading || isGetExplainKeywordsLoading : false
              }
            >
              <div className="flex items-center gap-2">
                {t(TABS.bot.label)}

                {isEnableExplainKeywords && isGetExplainKeywordsLoading && (
                  <div className="ml-1 flex items-center">
                    <div className="h-[6px] w-[6px] animate-pulse rounded-full bg-subdued"></div>
                    <div
                      className="ml-1 h-[6px] w-[6px] animate-pulse rounded-full bg-subdued"
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                    <div
                      className="ml-1 h-[6px] w-[6px] animate-pulse rounded-full bg-subdued"
                      style={{ animationDelay: '0.4s' }}
                    ></div>
                  </div>
                )}

                {/* New data indicator */}
                {hasNewBotData && !hasViewedBotTab && (
                  <span className="relative ml-2 flex h-2 w-2">
                    <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-amber-400 opacity-75"></span>
                    <span className="relative inline-flex h-2 w-2 rounded-full bg-amber-500"></span>
                  </span>
                )}
              </div>
            </TabsTrigger>
            {/* KEYWORDS */}
            <TabsTrigger
              value={TABS.keywords.value}
              className={cn(
                baseTabClassName.base,
                activeTab === TABS.keywords.value
                  ? baseTabClassName.active
                  : baseTabClassName.inactive,
              )}
              ref={(el) => {
                tabRefs.current[TABS.keywords.value] = el
              }}
            >
              {t(TABS.keywords.label)}{' '}
              {processedQueryTerms?.length > 0 &&
                `(${getFormattedTotalCount(TABS.keywords.value)})`}
            </TabsTrigger>
            {/* FACULTIES */}
            <TabsTrigger
              value={TABS.faculties.value}
              className={cn(
                baseTabClassName.base,
                activeTab === TABS.faculties.value
                  ? baseTabClassName.active
                  : baseTabClassName.inactive,
              )}
              ref={(el) => {
                tabRefs.current[TABS.faculties.value] = el
              }}
            >
              {t(TABS.faculties.label)}{' '}
              {processedQueryTerms?.length > 0 &&
                `(${getFormattedTotalCount(TABS.faculties.value)})`}
            </TabsTrigger>
            {/* PRODUCTS */}
            <TabsTrigger
              value={TABS.products.value}
              className={cn(
                baseTabClassName.base,
                activeTab === TABS.products.value
                  ? baseTabClassName.active
                  : baseTabClassName.inactive,
              )}
              ref={(el) => {
                tabRefs.current[TABS.products.value] = el
              }}
            >
              {t(TABS.products.label)}{' '}
              {processedQueryTerms?.length > 0 &&
                `(${getFormattedTotalCount(TABS.products.value)})`}
            </TabsTrigger>
          </TabsList>
        </div>
        {/* SUMMARY */}
        <TabsContent value={TABS.summary.value}>
          {isEmptySeachResult ? (
            <EmptyList />
          ) : (
            <div className="space-y-4">
              <KeywordsSearchResult
                keywords={keywords}
                isGetKeywordsLoading={isGetKeywordsLoading}
                showAllResults={false}
                onShowMoreClick={() => onShowMoreClick(TABS.keywords.value)}
                showShowMoreButton={Boolean(
                  keywords?.pages[0]?.totalDocs && keywords?.pages[0]?.totalDocs > 2,
                )}
                favoriteKeywords={favoriteKeywords}
                shouldShowTotalDocs={processedQueryTerms?.length > 0}
                isShowEmptyState={false}
              ></KeywordsSearchResult>

              <FacultiesSearchResult
                faculties={faculties}
                isGetFacultiesLoading={isGetFacultiesLoading}
                showAllResults={false}
                onShowMoreClick={() => onShowMoreClick(TABS.faculties.value)}
                showShowMoreButton={Boolean(
                  faculties?.pages[0]?.totalDocs && faculties?.pages[0]?.totalDocs > 2,
                )}
                isShowEmptyState={false}
                shouldShowTotalDocs={processedQueryTerms?.length > 0}
                isNative={isNativeView || isNative}
              ></FacultiesSearchResult>
              {!isNative && !isNativeView ? (
                <ProductsSearchResult
                  // key={query}
                  medicines={medicines}
                  isGetMedicinesLoading={isGetMedicinesLoading}
                  showAllResults={false}
                  onShowMoreClick={() => onShowMoreClick(TABS.products.value)}
                  showShowMoreButton={Boolean(
                    medicines?.pages[0]?.totalDocs && medicines?.pages[0]?.totalDocs > 6,
                  )}
                  isShowEmptyState={false}
                  shouldShowTotalDocs={processedQueryTerms?.length > 0}
                ></ProductsSearchResult>
              ) : (
                <ProductsV2SearchResult
                  // key={query}
                  products={productsV2}
                  isGetProductsLoading={isGetProductsV2Loading}
                  showAllResults={false}
                  onShowMoreClick={() => onShowMoreClick(TABS.products.value)}
                  showShowMoreButton={Boolean(
                    productsV2?.pages[0]?.totalDocs && productsV2?.pages[0]?.totalDocs > 6,
                  )}
                  isShowEmptyState={false}
                  shouldShowTotalDocs={processedQueryTerms?.length > 0}
                  isNative={isNative || isNativeView}
                ></ProductsV2SearchResult>
              )}
            </div>
          )}
        </TabsContent>
        {/* HICO-BOT */}
        <TabsContent value={TABS.bot.value}>
          <>
            {/* EXPLANATION KEYWORDS */}
            {user ? (
              <>
                {(medicines?.pages[0]?.totalDocs === 0 || faculties?.pages[0]?.totalDocs === 0) && (
                  <KeywordsExplanation
                    explanations={explainKeywords?.explainations ?? []}
                    isLoading={isGetExplainKeywordsLoading}
                  />
                )}
              </>
            ) : (
              <>
                {(medicines?.pages[0]?.totalDocs === 0 || faculties?.pages[0]?.totalDocs === 0) && (
                  <KeywordsExplanationAccess />
                )}
              </>
            )}
          </>
        </TabsContent>
        {/* FACULTIES */}
        <TabsContent value={TABS.faculties.value}>
          <FacultiesSearchResult
            faculties={faculties}
            isGetFacultiesLoading={isGetFacultiesLoading}
            showAllResults={true}
            isInfiniteScroll={true}
            fetchNextPage={fetchNextPageFaculties}
            hasNextPage={hasNextPageFaculties}
            isFetchingNextPage={isFetchingNextPageFaculties}
            shouldShowTotalDocs={processedQueryTerms?.length > 0}
            isNative={isNativeView || isNative}
          ></FacultiesSearchResult>
        </TabsContent>
        {/* KEYWORDS */}
        <TabsContent value={TABS.keywords.value}>
          <KeywordsSearchResult
            keywords={keywords}
            isGetKeywordsLoading={isGetKeywordsLoading}
            showAllResults={true}
            isInfiniteScroll={true}
            fetchNextPage={fetchNextPageKeywords}
            hasNextPage={hasNextPageKeywords}
            isFetchingNextPage={isFetchingNextPageKeywords}
            favoriteKeywords={favoriteKeywords}
            shouldShowTotalDocs={processedQueryTerms?.length > 0}
          ></KeywordsSearchResult>
        </TabsContent>
        {/* PRODUCTS */}
        <TabsContent value={TABS.products.value}>
          {!isNative && !isNativeView ? (
            <ProductsSearchResult
              medicines={medicines}
              isGetMedicinesLoading={isGetMedicinesLoading}
              showAllResults={true}
              isInfiniteScroll={true}
              fetchNextPage={fetchNextPageMedicines}
              hasNextPage={hasNextPageMedicines}
              isFetchingNextPage={isFetchingNextPageMedicines}
              shouldShowTotalDocs={processedQueryTerms?.length > 0}
            ></ProductsSearchResult>
          ) : (
            <ProductsV2SearchResult
              products={productsV2}
              isGetProductsLoading={isGetProductsV2Loading}
              showAllResults={true}
              isInfiniteScroll={true}
              fetchNextPage={fetchNextPageProductsV2}
              hasNextPage={hasNextPageProductsV2}
              isFetchingNextPage={isFetchingNextPageProductsV2}
              shouldShowTotalDocs={processedQueryTerms?.length > 0}
              isNative={isNative || isNativeView}
            ></ProductsV2SearchResult>
          )}
        </TabsContent>
      </Tabs>

      {/* Populated Search Keywords */}
      <PopulatedSearchKeywords
        onKeywordClick={(keyword) => handleListSearchKeywordsChange([keyword])}
      />
    </div>
  )
}

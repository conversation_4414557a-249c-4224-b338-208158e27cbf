import Image from 'next/image'

import ImageTest from '@/assets/images/body-part-2.png'
import { Media, Product } from '@/payload-types'

type ProductItemType = {
  isNew?: boolean
  productData: Product
}

const ProductItem: React.FC<ProductItemType> = ({ isNew = false, productData }) => {
  if (!productData) return null
  const { stores, title, heroImage } = productData

  const banner = (heroImage as Media) || {}

  const storeIcons = stores?.map((store) => store['medicine-store']?.['logo'])

  return (
    <div
      className={`flex cursor-pointer flex-col gap-2 rounded-lg px-3 py-4 ${isNew ? 'bg-yellow-100' : 'bg-custom-background-hover'}`}
    >
      {/* Related */}
      <div className="flex min-h-5 items-center gap-3">
        {storeIcons?.map((store) => {
          const iconURL = store?.sizes?.thumbnail?.url || store?.url
          return (
            iconURL && (
              <div className="relative h-[18px] w-[18px]" key={store?.id}>
                <Image key={store.id} alt={'logo'} src={iconURL} height={20} width={19} />
              </div>
            )
          )
        })}
      </div>

      <div className="h-[120px] w-full">
        {banner.url && (
          <Image src={ImageTest} alt={'label'} className="h-full w-full object-cover" />
        )}
      </div>

      <div className="typo-body-6 line-clamp-2">{title}</div>
    </div>
  )
}

export default ProductItem

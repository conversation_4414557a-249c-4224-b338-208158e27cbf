import { useTranslations } from 'next-intl'
import { Swiper, SwiperSlide } from 'swiper/react'
import ProductItem from './ProductItem'

const ProductList: React.FC = () => {
  const t = useTranslations()

  return (
    <>
      <div className="typo-body-3 mt-3 flex items-center gap-3">{t('MES-660')} (21)</div>

      <div className="mt-3">
        <Swiper spaceBetween={12} slidesPerView={6.5}>
          <SwiperSlide>
            <ProductItem />
          </SwiperSlide>
        </Swiper>
      </div>
    </>
  )
}

export default ProductList

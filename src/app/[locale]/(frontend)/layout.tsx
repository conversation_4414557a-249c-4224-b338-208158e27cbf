import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { Inter } from 'next/font/google'
import React from 'react'

import { routing } from '@/i18n/routing'
import '@/styles/global.scss'
import { getServerSideURL } from '@/utilities/getURL'
import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google'
import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages, getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import Script from 'next/script'
import * as process from 'node:process'
import 'photoswipe/dist/photoswipe.css'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { ChatwootWidget } from '@/components/ChatwootWidget/ChatwootWidget'
import { cookies } from 'next/headers'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})
export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  const { locale } = await params
  // setRequestLocale(locale)
  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as any)) {
    notFound()
  }
  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()
  return (
    <html className={inter.className} lang={locale} suppressHydrationWarning>
      <head>
        <InitTheme />
        <link href="/favicon-wap.png" rel="icon" sizes="50x50" />
        {!isNative && (
          <meta
            name="google-adsense-account"
            content={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT}
          />
        )}
      </head>

      <body suppressHydrationWarning>
        <>
          {/* Setup GTM and GA4 */}
          <GoogleTagManager gtmId={process.env.GTM_ID ?? ''} />
          <GoogleAnalytics gaId={process.env.MEASUREMENT_ID ?? ''} />
        </>
        <ChatwootWidget></ChatwootWidget>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Providers>{children}</Providers>
        </NextIntlClientProvider>
        {!isNative && (
          <Script
            async
            src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT}`}
            crossOrigin="anonymous"
            strategy="afterInteractive"
            data-overlays="bottom"
          />
        )}
      </body>
    </html>
  )
}

export async function generateMetadata() {
  const t = await getTranslations()
  const local = await getLocale()
  return {
    metadataBase: new URL(getServerSideURL()),

    // Page Title and Description (Critical for SEO)
    title: t('MES-199'),
    description: t('MES-200'),

    // Open Graph Metadata (For Facebook, LinkedIn, etc.)
    openGraph: {
      type: 'website',
      title: t('MES-199'),
      description: t('MES-201'),
      url: getServerSideURL(),
      siteName: 'HICO',
      images: [
        {
          url: '/logo-open-graph.jpg', // Replace with the URL of your Open Graph image

          alt: t('MES-202'),
        },
      ],
      locale: local, // Vietnamese locale
    },

    // Twitter Metadata
    twitter: {
      card: 'summary_large_image',
      site: '@japanmedicalgate', // Update to your official Twitter handle
      title: t('MES-200'),
      description: t('MES-201'),
      images: ['/logo-open-graph.jpg'], // Replace with the URL of your Twitter image
    },

    // Robots Meta Tag (Control Crawling and Indexing)
    robots: {
      index: true,
      follow: true,
      'max-snippet': -1, // Full snippet
      'max-image-preview': 'large', // Large image previews
      'max-video-preview': -1, // Full video preview
    },

    // Canonical URL (Avoid duplicate content issues)
    alternates: {
      canonical: getServerSideURL(),
    },

    // Additional Metadata (Optional)
    keywords: [
      'HICO',
      'ジャパンメディカルゲート',
      'Y tế Nhật Bản',
      '日本の医療',
      'Chăm sóc sức khỏe Việt Nam',
      'ベトナムの健康管理',
      'Cổng thông tin y tế',
      '医療情報ゲートウェイ',
      'Bệnh viện Nhật Bản',
      '日本の病院',
      'Dịch vụ y tế quốc tế',
      '国際医療サービス',
      'health',
      '健康',
      'WAP',
      'WAP',
    ],
  }
}

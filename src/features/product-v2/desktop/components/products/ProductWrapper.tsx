'use client'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useGetInfiniteProductsV2 } from '@/features/product-v2/hooks/query/useGetInfiniteProductsV2'
import { useLocale } from 'next-intl'
import FeaturedProductList from './FeaturedProductList'
import ProductList from './ProductList'
import ProductSkeleton from './ProductSkeleton'
import { useState } from 'react'

const ProductWrapper: React.FC = () => {
  const locale = useLocale();

  const [typeFilter, setTypeFilter] = useState<ProductV2TypeEnum[]>([
    ProductV2TypeEnum.MEDICINE,
  ])

  const { isLoading, productsV2: featuredProduct } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 12,
      categories: [],
      where: {
        and: [
          {
            featured: {
              equals: true,
            },
          },
          {
            type: {
              in: typeFilter,
            },
          },
        ],
      },
    },
  })

  const dataProductFeatured = featuredProduct?.pages[0]?.docs

  return isLoading ? (
    <ProductSkeleton />
  ) : (
    <>
      <FeaturedProductList featureProduct={dataProductFeatured ?? []} />
      <ProductList />
    </>
  )
}

export default ProductWrapper

export enum NativeAppRoutesEnum {
  USER = 'USER',
  MEDICAL_HANDBOOK = 'MEDICAL_HANDBOOK',
  SUBSCRIPTION_MODIFY = 'SUBSCRIPTION_MODIFY',
  HOME = 'HOME',
  SEARCH_SUMMARY = 'SEARCH_SUMMARY',
  LOGIN = 'LOGIN',
  REGISTER = 'REGISTER',
  VERIFY_EMAIL = 'VERIFY_EMAIL',
  RESET_PASSWORD = 'RESET_PASSWORD',
  RESEND_VERIFY_EMAIL = 'RESEND_VERIFY_EMAIL',
  TUTORIAL = 'TUTORIAL',
  EXAMINATION = 'EXAMINATION',
  POSTS = 'POSTS',
  PRODUCTS = 'PRODUCTS',
  PRODUCTS_MEDICINES = 'PRODUCTS_MEDICINES',
  PRODUCTS_SUPPLEMENTS = 'PRODUCTS_SUPPLEMENTS',
  MEDICAL_DICTIONARY = 'MEDICAL_DICTIONARY',
  MEDICAL_DICTIONARY_SEARCH = 'MEDICAL_DICTIONARY_SEARCH',
  MEDICAL_HANDBOOK_FACULTIES = 'MEDICAL_HANDBOOK_FACULTIES',
  BODY_PARTS = 'BODY_PARTS',
  CHAT_BOT = 'CHAT_BOT',
  CHAT_BOT_SEARCH_MEDICINE = 'CHAT_BOT_SEARCH_MEDICINE',
  FAVORITE_MEDICINES = 'FAVORITE_MEDICINES',
  TRANSACTION_HISTORY = 'TRANSACTION_HISTORY',
  MANAGE_SUBSCRIPTIONS = 'MANAGE_SUBSCRIPTIONS',
  MEDICINE_BODY_PARTS = 'MEDICINE_BODY_PARTS',
  PRODUCTS_LIST = 'PRODUCTS_LIST',
  PRODUCTS_DETAIL = 'PRODUCTS_DETAIL',
  PROFILE = 'PROFILE',
  EDIT_PROFILE = 'EDIT_PROFILE',
  SWITCH_LANGUAGE = 'SWITCH_LANGUAGE',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  SEND_RESET_PASSWORD_EMAIL_SUCCESS = 'SEND_RESET_PASSWORD_EMAIL_SUCCESS',
  PRODUCTS_DETAIL_V2 = 'PRODUCTS_DETAIL_V2',
}

export const NATIVE_APP_ROUTES: Record<
  string,
  {
    path: string
    name: string
    tabName?: string
    tabPath?: string
    authRequired: boolean | null
    children?: Record<string, any>
    label?: Record<string, string>
  }
> = {
  [NativeAppRoutesEnum.HOME]: {
    path: '/',
    name: 'index',
    authRequired: true,
  },
  [NativeAppRoutesEnum.USER]: {
    path: '/user',
    name: 'user',
    authRequired: true,
  },
  [NativeAppRoutesEnum.MEDICAL_HANDBOOK]: {
    path: '/medical-handbook',
    name: 'medical-handbook',
    authRequired: true,
    tabName: 'medical-handbook-tab',
    tabPath: '/medical-handbook-tab',
  },
  [NativeAppRoutesEnum.BODY_PARTS]: {
    path: '/body-parts',
    name: 'body-parts',
    authRequired: true,
  },
  [NativeAppRoutesEnum.PRODUCTS]: {
    path: '/products',
    name: 'products',
    authRequired: true,
    tabName: 'products-tab',
    tabPath: '/products-tab',
    children: {
      [NativeAppRoutesEnum.MEDICINE_BODY_PARTS]: {
        path: '/products/medicine-body-parts',
        name: 'products-medicine-body-parts',
        authRequired: true,
      },
      [NativeAppRoutesEnum.PRODUCTS_LIST]: {
        path: '/products/list',
        name: 'products-list',
        authRequired: true,
      },
      [NativeAppRoutesEnum.PRODUCTS_DETAIL]: {
        path: '/products/detail',
        name: 'products-detail',
        authRequired: true,
      },
      [NativeAppRoutesEnum.PRODUCTS_DETAIL_V2]: {
        path: '/products/detail-v2',
        name: 'products-detail-v2',
        authRequired: null,
      },
    },
  },
  [NativeAppRoutesEnum.PRODUCTS_MEDICINES]: {
    path: '/products/medicines',
    name: 'products-medicines',
    authRequired: true,
  },
  [NativeAppRoutesEnum.MEDICAL_DICTIONARY]: {
    path: '/medical-dictionary',
    name: 'medical-dictionary',
    authRequired: true,
    tabName: 'medical-dictionary-tab',
    tabPath: '/medical-dictionary-tab',
  },
  [NativeAppRoutesEnum.MEDICAL_HANDBOOK_FACULTIES]: {
    path: '/medical',
    name: 'medical',
    authRequired: true,
  },
  [NativeAppRoutesEnum.POSTS]: {
    path: '/posts',
    name: 'posts',
    authRequired: true,
    tabName: 'posts-tab',
    tabPath: '/posts-tab',
  },
  [NativeAppRoutesEnum.CHAT_BOT]: {
    path: '/chatbot',
    name: 'chatbot',
    authRequired: true,
    children: {
      [NativeAppRoutesEnum.CHAT_BOT_SEARCH_MEDICINE]: {
        path: '/chatbot/search-medicine',
        name: 'chatbot-search-medicine',
        authRequired: true,
      },
    },
  },
  [NativeAppRoutesEnum.EXAMINATION]: {
    path: '/examination',
    name: 'examination',
    authRequired: true,
  },
  [NativeAppRoutesEnum.SEARCH_SUMMARY]: {
    path: '/search-summary',
    name: 'search-summary',
    authRequired: true,
  },
  [NativeAppRoutesEnum.PROFILE]: {
    path: '/user/profile',
    name: 'profile',
    authRequired: true,
  },
  [NativeAppRoutesEnum.EDIT_PROFILE]: {
    path: '/user/profile/edit-profile',
    name: 'edit-profile',
    authRequired: true,
  },
  [NativeAppRoutesEnum.SWITCH_LANGUAGE]: {
    path: '/user/profile/switch-language',
    name: 'switch-language',
    authRequired: true,
  },
  [NativeAppRoutesEnum.LOGIN]: {
    path: '/(auth)/login',
    name: 'login',
    authRequired: false,
  },
  [NativeAppRoutesEnum.REGISTER]: {
    path: '/(auth)/register',
    name: 'register',
    authRequired: false,
  },
  [NativeAppRoutesEnum.VERIFY_EMAIL]: {
    path: '/(auth)/verify-email',
    name: 'verify-email',
    authRequired: false,
  },
  [NativeAppRoutesEnum.FAVORITE_MEDICINES]: {
    path: '/user/favorite-medicine',
    name: 'favorite-medicine',
    authRequired: true,
  },
  [NativeAppRoutesEnum.CHANGE_PASSWORD]: {
    path: '/user/profile/change-password',
    name: 'change-password',
    authRequired: true,
  },
  [NativeAppRoutesEnum.FORGOT_PASSWORD]: {
    path: '/(auth)/forgot-password',
    name: 'forgot-password',
    authRequired: false,
  },
  [NativeAppRoutesEnum.SEND_RESET_PASSWORD_EMAIL_SUCCESS]: {
    path: '/(auth)/forgot-password/send-reset-password-email-success',
    name: 'send-reset-password-email-success',
    authRequired: false,
  },
}

export const PROTECTED_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
  Object.entries(NATIVE_APP_ROUTES)
    .filter(([_, route]) => route.authRequired === true)
    .map(([key, route]) => [key, route.path]),
)

export const GUEST_ONLY_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
  Object.entries(NATIVE_APP_ROUTES)
    .filter(([_, route]) => route.authRequired === false)
    .map(([key, route]) => [key, route.path]),
)

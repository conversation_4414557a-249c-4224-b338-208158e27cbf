import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

const ProductSkeleton: React.FC = () => {
  return (
    <>
      <div className="mt-3 grid grid-cols-6 gap-3">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="flex flex-col gap-3 rounded-lg bg-neutral-50 px-3 py-4">
            <Skeleton className="h-[140px] w-full" />
            <Skeleton className="h-[40px] w-full" />
          </div>
        ))}
      </div>
    </>
  )
}

export default ProductSkeleton

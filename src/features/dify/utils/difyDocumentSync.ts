import { LocaleEnum } from '@/enums/locale.enum'
import { DIFY_DATASETS, DIFY_DOCUMENT_CONFIG } from '@/features/dify/constants/dify.constant'
import { difyService } from '@/features/dify/services/dify.service'
import { CollectionSlug, PayloadRequest } from 'payload'

export interface DifyDocumentSyncConfig {
  configKey: string
  datasetId: string
  collection: string
  difyDocumentIdField: string
  documentPayload: {
    name: string
    text: string
  }
  metadataPayload?: {
    operation_data: Array<{
      document_id: string
      metadata_list: any
    }>
  }
}

// Helper function to check if document exists in Dify
export async function checkDocumentExists({
  datasetId,
  documentId,
}: {
  datasetId: string
  documentId: string
}): Promise<boolean> {
  try {
    const existingDocument = await difyService.getDocument({
      datasetId,
      documentId,
    })
    return !!existingDocument
  } catch (error: any) {
    if (error?.status === 404 || error?.response?.status === 404) {
      return false
    }
    // Log other errors but don't throw to avoid breaking execution
    console.error('Error checking document existence:', error)
    return false
  }
}

// Helper function to update existing Dify document
export async function updateExistingDifyDocument({
  config,
  documentId,
  req,
  docId,
  mergedText,
  locale,
}: {
  config: DifyDocumentSyncConfig
  documentId: string
  req: PayloadRequest
  docId: string
  mergedText: string
  locale?: string
}) {
  const response = await difyService.updateDocumentWithText({
    payload: {
      ...config.documentPayload,
      ...DIFY_DOCUMENT_CONFIG[config.configKey],
    },
    datasetId: config.datasetId,
    documentId,
  })

  if (config.metadataPayload) {
    config.metadataPayload.operation_data[0].document_id = response.document.id
    await difyService.updateDocumentMetadata({
      payload: config.metadataPayload,
      datasetId: config.datasetId,
    })
  }

  await updateDocumentFields({
    req,
    collection: config.collection as CollectionSlug,
    docId,
    data: { mergedText },
    locale,
  })
}

// Helper function to create new Dify document
export async function createNewDifyDocument({
  config,
  req,
  doc,
  mergedText,
}: {
  config: DifyDocumentSyncConfig
  req: PayloadRequest
  doc: any
  mergedText: string
}) {
  const response = await difyService.createDocumentFromText({
    payload: {
      ...config.documentPayload,
      ...DIFY_DOCUMENT_CONFIG[config.configKey],
    },
    datasetId: config.datasetId,
  })

  if (response?.document?.id) {
    if (config.metadataPayload) {
      config.metadataPayload.operation_data[0].document_id = response.document.id
      await difyService.updateDocumentMetadata({
        payload: config.metadataPayload,
        datasetId: config.datasetId,
      })
    }

    const updateData: Record<string, any> = {
      [config.difyDocumentIdField]: response.document.id,
      mergedText,
    }

    await updateDocumentFields({
      req,
      collection: config.collection as CollectionSlug,
      docId: doc.id,
      data: updateData,
    })

    doc[config.difyDocumentIdField] = response.document.id
    doc.mergedText = mergedText
  } else {
    console.warn('Dify document created but no document ID returned in response')
  }
}

// Helper function to update document fields via API
export async function updateDocumentFields({
  req,
  collection,
  docId,
  data,
  locale,
}: {
  req: PayloadRequest
  collection: CollectionSlug
  docId: string
  data: Record<string, any>
  locale?: string
}) {
  try {
    const res = await req.payload.update({
      collection,
      id: docId,
      data,
      context: {
        fromAfterChangeHook: true,
      },
      overrideAccess: true, // Bypass access control and validation
      req,
      disableTransaction: true,
      locale: (locale as LocaleEnum) || 'vi',
    })
  } catch (error) {
    console.error(`Failed to update ${collection} fields for ${docId}:`, error)
  }
}

// Main sync function that handles the common logic
export async function syncDifyDocument({
  config,
  currentDifyDocumentId,
  req,
  doc,
  mergedText,
  locale,
}: {
  config: DifyDocumentSyncConfig
  currentDifyDocumentId: string | null
  req: PayloadRequest
  doc: any
  mergedText: string
  locale?: string
}) {
  try {
    if (currentDifyDocumentId) {
      // We have dify ID, check if document exists
      const documentExists = await checkDocumentExists({
        datasetId: config.datasetId,
        documentId: currentDifyDocumentId,
      })

      if (documentExists) {
        // Document exists, UPDATE it
        await updateExistingDifyDocument({
          config,
          documentId: currentDifyDocumentId,
          req,
          docId: doc.id,
          mergedText,
          locale,
        })
      } else {
        // Document doesn't exist, CREATE new one
        console.log(
          `Document ${currentDifyDocumentId} not found in Dify, creating new document instead`,
        )
        await createNewDifyDocument({ config, req, doc, mergedText })
      }
    } else {
      // No dify ID, CREATE new document
      await createNewDifyDocument({ config, req, doc, mergedText })
    }
  } catch (error) {
    console.error('Failed to sync with Dify:', error)
  }
}

// Helper function to delete a single Dify document
export async function deleteDifyDocument({
  datasetId,
  documentId,
}: {
  datasetId: string
  documentId: string
}) {
  try {
    await difyService.deleteDocument({
      datasetId,
      documentId,
    })
    console.log(`Successfully deleted Dify document: ${documentId}`)
  } catch (error: any) {
    if (error?.status === 404 || error?.response?.status === 404) {
      console.log(`Document ${documentId} not found in Dify, already deleted`)
    } else {
      console.error(`Failed to delete Dify document ${documentId}:`, error)
    }
  }
}

// Main function to handle document deletion for different collections
export async function handleDifyDocumentDeletion({
  doc,
  collection,
}: {
  doc: any
  collection: 'posts' | 'medicines' | 'keywords' | 'faculties' | 'products'
}) {
  try {
    switch (collection) {
      case 'posts': {
        const difyDocumentId = doc.difyDocumentId

        if (difyDocumentId) {
          // Try to delete from both VI and JA datasets
          await Promise.allSettled([
            deleteDifyDocument({
              datasetId: DIFY_DATASETS.POSTS_VI,
              documentId: difyDocumentId,
            }),
            deleteDifyDocument({
              datasetId: DIFY_DATASETS.POSTS_JA,
              documentId: difyDocumentId,
            }),
          ])
        } else {
          console.log(`No Dify document ID found for post: ${doc.id}`)
        }
        break
      }

      case 'medicines': {
        const difyDocumentId = doc.difyDocumentId
        if (difyDocumentId) {
          await deleteDifyDocument({
            datasetId: DIFY_DATASETS.MEDICINES,
            documentId: difyDocumentId,
          })
        } else {
          console.log(`No Dify document ID found for medicine: ${doc.id}`)
        }
        break
      }

      case 'keywords': {
        const difyDocumentId = doc.difyDocumentId
        if (difyDocumentId) {
          await deleteDifyDocument({
            datasetId: DIFY_DATASETS.KEYWORDS,
            documentId: difyDocumentId,
          })
        } else {
          console.log(`No Dify document ID found for keyword: ${doc.id}`)
        }
        break
      }

      case 'faculties': {
        const difyDocumentId = doc.difyDocumentId
        if (difyDocumentId) {
          await deleteDifyDocument({
            datasetId: DIFY_DATASETS.FACULTIES,
            documentId: difyDocumentId,
          })
        } else {
          console.log(`No Dify document ID found for faculty: ${doc.id}`)
        }
        break
      }

      case 'products': {
        const difyDocumentId = doc.difyDocumentId
        if (difyDocumentId) {
          await deleteDifyDocument({
            datasetId: DIFY_DATASETS.PRODUCTS,
            documentId: difyDocumentId,
          })
        } else {
          console.log(`No Dify document ID found for product: ${doc.id}`)
        }
        break
      }

      default:
        console.warn(`Unknown collection: ${collection}`)
        break
    }
  } catch (error) {
    console.error(`Failed to delete Dify documents for ${collection}:`, error)
  }
}

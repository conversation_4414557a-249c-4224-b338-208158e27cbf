'use client'

import { Input } from '@/components/ui/Input/Input'
import { useDebounce } from '@/utilities/useDebounce'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useRef, useState } from 'react'

// image
import SearchIcon from '@/assets/icons/search-normal.svg'
import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'
import CameraIcon from '@/assets/icons/camera-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import { useDialog } from '@/hooks/common/useDialog'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product-v2/constants'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'

const MedicineHeader: React.FC = ({}) => {
  const t = useTranslations()
  const isInitializedRef = useRef<boolean>(false)

  const [inputValue, setInputValue] = useState<string>('')

  const [selectedCategories, setSelectedCategories] = useState<ProductV2TypeEnum>(
    ProductV2TypeEnum.MEDICINE,
  )

  const debouncedSearchValue = useDebounce(inputValue, 500)

  const { openDialog } = useDialog()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    isInitializedRef.current = true

    setInputValue(e.target.value)
  }

  const handleClearSearch = () => {
    setInputValue('')
  }

  const handleSelectCategory = (category: ProductV2TypeEnum) => {
    setSelectedCategories(category)
  }

  const handleOpenCamera = () => {}

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="typo-heading-7 text-primary-500">{t('MES-561')}</div>

        <div className="flex items-center gap-3">
          <div className="relative">
            <Input
              placeholder={t('MES-66')}
              className="min-w-[380px] rounded-lg px-4 py-2 pr-6"
              value={inputValue ?? ''}
              onChange={handleInputChange}
            />
            <Image
              alt={inputValue ? 'clear search' : 'search'}
              src={inputValue ? CircleClose : SearchIcon}
              width={20}
              height={20}
              className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
              onClick={inputValue ? handleClearSearch : undefined}
            />
          </div>

          <Button onClick={handleOpenCamera} variant={'blank'} className="p-0">
            <Image src={CameraIcon} alt="camera" width={32} height={32} />
          </Button>
        </div>
      </div>

      <div className="mt-3 flex items-center gap-3">
        {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((product) => (
          <div
            onClick={() => handleSelectCategory(product.value)}
            key={product.label}
            className={`typo-body-6 cursor-pointer rounded-xl border ${product.value === selectedCategories ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-50 text-subdued'} px-3 py-[6px]`}
          >
            {t(product.translationKey)}
          </div>
        ))}
      </div>
    </>
  )
}

export default MedicineHeader

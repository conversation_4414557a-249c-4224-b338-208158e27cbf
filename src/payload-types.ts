/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    pages: Page;
    posts: Post;
    media: Media;
    categories: Category;
    users: User;
    'post-categories': PostCategory;
    medicines: Medicine;
    'medicine-categories': MedicineCategory;
    'medicine-buy-button': MedicineBuyButton;
    coupons: Coupon;
    kols: Kol;
    campaigns: Campaign;
    'favorite-medicines': FavoriteMedicine;
    subscriptions: Subscription;
    'login-sessions': LoginSession;
    versions: Version;
    symptoms: Symptom;
    keywords: Keyword;
    summarizes: Summarize;
    questions: Question;
    faculties: Faculty;
    'body-parts': BodyPart;
    'user-search-histories': UserSearchHistory;
    'email-subscriptions': EmailSubscription;
    'user-subscriptions': UserSubscription;
    transactions: Transaction;
    'payment-methods': PaymentMethod;
    'patient-group': PatientGroup;
    'medicine-type': MedicineType;
    'examination-forms': ExaminationForm;
    'examination-questions': ExaminationQuestion;
    'user-examinations': UserExamination;
    'dietary-supplement-categories': DietarySupplementCategory;
    'favorite-keywords': FavoriteKeyword;
    'image-search-conversations': ImageSearchConversation;
    'image-search-messages': ImageSearchMessage;
    'dynamic-popups': DynamicPopup;
    'popup-tracking': PopupTracking;
    'coupon-tracking': CouponTracking;
    facts: Fact;
    'search-tips': SearchTip;
    'live-streams': LiveStream;
    'support-requests': SupportRequest;
    products: Product;
    'product-age-groups': ProductAgeGroup;
    'product-categories': ProductCategory;
    'favorite-products': FavoriteProduct;
    redirects: Redirect;
    forms: Form;
    'form-submissions': FormSubmission;
    search: Search;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    pages: PagesSelect<false> | PagesSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    'post-categories': PostCategoriesSelect<false> | PostCategoriesSelect<true>;
    medicines: MedicinesSelect<false> | MedicinesSelect<true>;
    'medicine-categories': MedicineCategoriesSelect<false> | MedicineCategoriesSelect<true>;
    'medicine-buy-button': MedicineBuyButtonSelect<false> | MedicineBuyButtonSelect<true>;
    coupons: CouponsSelect<false> | CouponsSelect<true>;
    kols: KolsSelect<false> | KolsSelect<true>;
    campaigns: CampaignsSelect<false> | CampaignsSelect<true>;
    'favorite-medicines': FavoriteMedicinesSelect<false> | FavoriteMedicinesSelect<true>;
    subscriptions: SubscriptionsSelect<false> | SubscriptionsSelect<true>;
    'login-sessions': LoginSessionsSelect<false> | LoginSessionsSelect<true>;
    versions: VersionsSelect<false> | VersionsSelect<true>;
    symptoms: SymptomsSelect<false> | SymptomsSelect<true>;
    keywords: KeywordsSelect<false> | KeywordsSelect<true>;
    summarizes: SummarizesSelect<false> | SummarizesSelect<true>;
    questions: QuestionsSelect<false> | QuestionsSelect<true>;
    faculties: FacultiesSelect<false> | FacultiesSelect<true>;
    'body-parts': BodyPartsSelect<false> | BodyPartsSelect<true>;
    'user-search-histories': UserSearchHistoriesSelect<false> | UserSearchHistoriesSelect<true>;
    'email-subscriptions': EmailSubscriptionsSelect<false> | EmailSubscriptionsSelect<true>;
    'user-subscriptions': UserSubscriptionsSelect<false> | UserSubscriptionsSelect<true>;
    transactions: TransactionsSelect<false> | TransactionsSelect<true>;
    'payment-methods': PaymentMethodsSelect<false> | PaymentMethodsSelect<true>;
    'patient-group': PatientGroupSelect<false> | PatientGroupSelect<true>;
    'medicine-type': MedicineTypeSelect<false> | MedicineTypeSelect<true>;
    'examination-forms': ExaminationFormsSelect<false> | ExaminationFormsSelect<true>;
    'examination-questions': ExaminationQuestionsSelect<false> | ExaminationQuestionsSelect<true>;
    'user-examinations': UserExaminationsSelect<false> | UserExaminationsSelect<true>;
    'dietary-supplement-categories': DietarySupplementCategoriesSelect<false> | DietarySupplementCategoriesSelect<true>;
    'favorite-keywords': FavoriteKeywordsSelect<false> | FavoriteKeywordsSelect<true>;
    'image-search-conversations': ImageSearchConversationsSelect<false> | ImageSearchConversationsSelect<true>;
    'image-search-messages': ImageSearchMessagesSelect<false> | ImageSearchMessagesSelect<true>;
    'dynamic-popups': DynamicPopupsSelect<false> | DynamicPopupsSelect<true>;
    'popup-tracking': PopupTrackingSelect<false> | PopupTrackingSelect<true>;
    'coupon-tracking': CouponTrackingSelect<false> | CouponTrackingSelect<true>;
    facts: FactsSelect<false> | FactsSelect<true>;
    'search-tips': SearchTipsSelect<false> | SearchTipsSelect<true>;
    'live-streams': LiveStreamsSelect<false> | LiveStreamsSelect<true>;
    'support-requests': SupportRequestsSelect<false> | SupportRequestsSelect<true>;
    products: ProductsSelect<false> | ProductsSelect<true>;
    'product-age-groups': ProductAgeGroupsSelect<false> | ProductAgeGroupsSelect<true>;
    'product-categories': ProductCategoriesSelect<false> | ProductCategoriesSelect<true>;
    'favorite-products': FavoriteProductsSelect<false> | FavoriteProductsSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    forms: FormsSelect<false> | FormsSelect<true>;
    'form-submissions': FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    header: Header;
    footer: Footer;
    'home-page-config': HomePageConfig;
    'ai-bot-configs': AiBotConfig;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
    'home-page-config': HomePageConfigSelect<false> | HomePageConfigSelect<true>;
    'ai-bot-configs': AiBotConfigsSelect<false> | AiBotConfigsSelect<true>;
  };
  locale: 'vi' | 'ja';
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      getAllUsers: TaskGetAllUsers;
      sendMailToUser: TaskSendMailToUser;
      cancelPlanSubscription: TaskCancelPlanSubscription;
      cancelTransaction: TaskCancelTransaction;
      checkAndCancelUserSubscriptionTask: TaskCheckAndCancelUserSubscriptionTask;
      countingSearchKeyword: TaskCountingSearchKeyword;
      saveMedicineAnalyzedByAI: TaskSaveMedicineAnalyzedByAI;
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: {
      resetConversationLimitWorkflow: WorkflowResetConversationLimitWorkflow;
      checkUserPlanWorkflow: WorkflowCheckUserPlanWorkflow;
    };
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: string;
  title: string;
  hero: {
    type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    links?:
      | {
          link: {
            type?: ('reference' | 'custom') | null;
            newTab?: boolean | null;
            reference?:
              | ({
                  relationTo: 'pages';
                  value: string | Page;
                } | null)
              | ({
                  relationTo: 'posts';
                  value: string | Post;
                } | null);
            url?: string | null;
            label: string;
            /**
             * Choose how the link should be rendered.
             */
            appearance?: ('default' | 'outline') | null;
          };
          id?: string | null;
        }[]
      | null;
    media?: (string | null) | Media;
  };
  layout: (CallToActionBlock | ContentBlock | MediaBlock | ArchiveBlock | FormBlock)[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: string;
  title: string;
  featured?: boolean | null;
  language?: ('vi' | 'ja') | null;
  heroImage?: (string | null) | Media;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  keywords?:
    | {
        keyword?: string | null;
        id?: string | null;
      }[]
    | null;
  relatedPosts?: (string | Post)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  authors?: (string | User)[] | null;
  populatedAuthors?:
    | {
        id?: string | null;
        name?: string | null;
      }[]
    | null;
  categories?: (string | null) | PostCategory;
  slug?: string | null;
  slugLock?: boolean | null;
  /**
   * This field is automatically populated from post title and post content editor
   */
  mergedText?: string | null;
  /**
   * Dify document ID for this post
   */
  difyDocumentId?: string | null;
  /**
   * Use this field to trigger Dify document sync (support bulk edit)
   */
  triggerDifyDocumentSync?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  name: string;
  roles?: ('admin' | 'user')[] | null;
  subscription?: (string | null) | Subscription;
  gender?: ('male' | 'female') | null;
  dob?: string | null;
  address?: string | null;
  settings?: {
    fontSize?: ('small' | 'normal' | 'large') | null;
    fontWeight?: ('thin' | 'normal' | 'bold') | null;
  };
  lastLogin?: string | null;
  avatar?: (string | null) | Media;
  connectedTo?: ('google' | 'facebook' | 'apple')[] | null;
  oauthAvatar?: string | null;
  subscriptionTime?: {
    startDate?: string | null;
    endDate?: string | null;
  };
  sendExpiredNotificationTime?: number | null;
  sendExpiredWarningNotificationCount?: number | null;
  hasAppliedInvitationCode?: boolean | null;
  appleSub?: string | null;
  isDeactivated?: boolean | null;
  isDeactivatedAt?: string | null;
  archiveEmail?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  _verified?: boolean | null;
  _verificationToken?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscriptions".
 */
export interface Subscription {
  id: string;
  name: string;
  type: 'free' | 'trial' | 'standard' | 'advanced';
  price: number;
  duration?: {
    amount?: number | null;
    unit?: ('day' | 'week' | 'month' | 'year') | null;
  };
  qrImage?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "post-categories".
 */
export interface PostCategory {
  id: string;
  title: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: string | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'posts' | null;
  categories?: (string | Category)[] | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'posts';
        value: string | Post;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: string;
  title: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock".
 */
export interface FormBlock {
  form: string | Form;
  enableIntro?: boolean | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'formBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms".
 */
export interface Form {
  id: string;
  title: string;
  fields?:
    | (
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            defaultValue?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'checkbox';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'country';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'email';
          }
        | {
            message?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'number';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            placeholder?: string | null;
            options?:
              | {
                  label: string;
                  value: string;
                  id?: string | null;
                }[]
              | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'select';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'state';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'text';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'textarea';
          }
      )[]
    | null;
  submitButtonLabel?: string | null;
  /**
   * Choose whether to display an on-page message or redirect to a different page after they submit the form.
   */
  confirmationType?: ('message' | 'redirect') | null;
  confirmationMessage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  redirect?: {
    url: string;
  };
  /**
   * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
   */
  emails?:
    | {
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicines".
 */
export interface Medicine {
  id: string;
  title: string;
  description?: string | null;
  availableIn: (string | Subscription)[];
  isDietarySupplement: boolean;
  patientGroup: (string | PatientGroup)[];
  type?: (string | MedicineType)[] | null;
  featured?: boolean | null;
  stores?:
    | {
        'medicine-store'?: (string | null) | MedicineBuyButton;
        url?: string | null;
        id?: string | null;
      }[]
    | null;
  heroImage: string | Media;
  uses?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  dosageForm?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  specification?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  ingredient?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  dosage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  contraindications?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  note?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  keywords?: (string | Keyword)[] | null;
  relatedMedicines?: (string | Medicine)[] | null;
  /**
   * For internal use only: helps categorize entries by body part.
   */
  bodyPart?: (string | BodyPart)[] | null;
  categories?: (string | MedicineCategory)[] | null;
  dietarySupplementCategories?: (string | DietarySupplementCategory)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  unverified?: boolean | null;
  AIAnalyzed?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  slug?: string | null;
  slugLock?: boolean | null;
  difyDocumentId?: string | null;
  mergedText?: string | null;
  /**
   * Use this field to trigger Dify document sync (support bulk edit)
   */
  triggerDifyDocumentSync?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-group".
 */
export interface PatientGroup {
  id: string;
  title: string;
  icon?: (string | null) | Media;
  type?: ('adult' | 'child') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-type".
 */
export interface MedicineType {
  id: string;
  name: string;
  title: string;
  icon?: (string | null) | Media;
  note?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-buy-button".
 */
export interface MedicineBuyButton {
  id: string;
  title: string;
  logo?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "keywords".
 */
export interface Keyword {
  id: string;
  name: string;
  hiragana: string;
  editable?: boolean | null;
  isCustom?: boolean | null;
  popupType?: ('day' | 'week' | 'month' | 'year' | 'other' | 'description' | 'fever') | null;
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  unverified?: boolean | null;
  /**
   * Select categories this keyword belongs to
   */
  categories?:
    | (
        | 'body_part'
        | 'medicine'
        | 'medicine_type'
        | 'allergy_type'
        | 'symptom_or_disease'
        | 'test_vaccine_treatment'
        | 'lifestyle'
        | 'medical_science'
        | 'other'
      )[]
    | null;
  relatedKeywords?: (string | Keyword)[] | null;
  relatedImages?: (string | Media)[] | null;
  audio?:
    | {
        language?: ('vi' | 'ja') | null;
        audio?: (string | null) | Media;
        id?: string | null;
      }[]
    | null;
  searchCount?: number | null;
  mergedText?: string | null;
  difyDocumentId?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "body-parts".
 */
export interface BodyPart {
  id: string;
  name: string;
  heroImage?: (string | null) | Media;
  rootBodyPart?: boolean | null;
  childBodyParts?: (string | BodyPart)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-categories".
 */
export interface MedicineCategory {
  id: string;
  title: string;
  bodyPart?: (string | BodyPart)[] | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dietary-supplement-categories".
 */
export interface DietarySupplementCategory {
  id: string;
  title: string;
  image?: (string | null) | Media;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "coupons".
 */
export interface Coupon {
  id: string;
  code: string;
  description?: string | null;
  amount?: number | null;
  used?: number | null;
  expiredAt?: string | null;
  isLimitedPerUser?: boolean | null;
  limit?: number | null;
  isExpired?: boolean | null;
  isActive?: boolean | null;
  campaign?: (string | null) | Campaign;
  version?: ('v1' | 'v2') | null;
  type?: ('trial-plan-offer' | 'discount' | 'premium-plan-offer') | null;
  discountAmount?: number | null;
  discountType?: ('percent' | 'fixedPrice') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "campaigns".
 */
export interface Campaign {
  id: string;
  name: string;
  description?: string | null;
  startAt: string;
  endAt: string;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "kols".
 */
export interface Kol {
  id: string;
  fullName: string;
  phone?: number | null;
  email?: string | null;
  description?: string | null;
  introduceCode?: (string | Coupon)[] | null;
  successSubscription?: number | null;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-medicines".
 */
export interface FavoriteMedicine {
  id: string;
  name?: string | null;
  user?: (string | null) | User;
  medicine: string | Medicine;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "login-sessions".
 */
export interface LoginSession {
  id: string;
  isActive?: boolean | null;
  user?: (string | null) | User;
  expiredAt?: string | null;
  agentInfo?: string | null;
  device?: string | null;
  os?: string | null;
  browser?: string | null;
  deviceType?: ('desktop' | 'mobile') | null;
  isMobileLogin?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "versions".
 */
export interface Version {
  id: string;
  name: string;
  shortDescription: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  releasedAt?: string | null;
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "symptoms".
 */
export interface Symptom {
  id: string;
  name?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "summarizes".
 */
export interface Summarize {
  id: string;
  name: string;
  sentence: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "questions".
 */
export interface Question {
  id: string;
  name: string;
  question: string;
  key?: string | null;
  note?: string | null;
  keywords?: (string | Keyword)[] | null;
  category: 'patient' | 'doctor';
  isMultipleChoices?: boolean | null;
  canSkipQuestion?: boolean | null;
  summarize?: (string | null) | Summarize;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faculties".
 */
export interface Faculty {
  id: string;
  name: string;
  description?: string | null;
  fallbackFaculty?: boolean | null;
  hasQuestions?: boolean | null;
  symptoms?: (string | Symptom)[] | null;
  bodyParts?: (string | BodyPart)[] | null;
  subscription?: (string | Subscription)[] | null;
  questions?:
    | {
        step: string;
        question: string | Question;
        id?: string | null;
      }[]
    | null;
  examinationForm?: (string | null) | ExaminationForm;
  icon?: (string | null) | Media;
  mergedText?: string | null;
  difyDocumentId?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "examination-forms".
 */
export interface ExaminationForm {
  id: string;
  name: string;
  description?: string | null;
  step: {
    title: string;
    questions: (string | ExaminationQuestion)[];
    id?: string | null;
  }[];
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "examination-questions".
 */
export interface ExaminationQuestion {
  id: string;
  name: string;
  fields?:
    | (
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            yesLabel?: string | null;
            noLabel?: string | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-yes-no-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            radioOptions: {
              label: string;
              type?: ('picker' | 'text') | null;
              id?: string | null;
            }[];
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-group-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            yesLabel?: string | null;
            noLabel?: string | null;
            openCheckboxLabel: string;
            checkboxQuestions?:
              | {
                  checkboxLabel: string;
                  checkboxQuestionLabel: string;
                  checkboxQuestionDescription?: string | null;
                  keywordCategory?:
                    | (
                        | 'body_part'
                        | 'medicine'
                        | 'medicine_type'
                        | 'allergy_type'
                        | 'symptom_or_disease'
                        | 'test_vaccine_treatment'
                        | 'lifestyle'
                        | 'medical_science'
                        | 'other'
                      )
                    | null;
                  id?: string | null;
                }[]
              | null;
            noteLabel?: string | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-yes-no-open-checkbox-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            yesLabel?: string | null;
            noLabel?: string | null;
            openGroupQuestionsLabel?: string | null;
            openGroupQuestions: (
              | {
                  label: string;
                  required?: boolean | null;
                  id?: string | null;
                  blockName?: string | null;
                  blockType: 'plain-text-question';
                }
              | {
                  label: string;
                  description?: string | null;
                  required?: boolean | null;
                  /**
                   * Control which gender this question will be shown to
                   */
                  genderVisibility?: ('all' | 'male' | 'female') | null;
                  type: 'single';
                  /**
                   * Keywords will be filtered by the selected category
                   */
                  keywordCategory?:
                    | (
                        | 'body_part'
                        | 'medicine'
                        | 'medicine_type'
                        | 'allergy_type'
                        | 'symptom_or_disease'
                        | 'test_vaccine_treatment'
                        | 'lifestyle'
                        | 'medical_science'
                        | 'other'
                      )[]
                    | null;
                  id?: string | null;
                  blockName?: string | null;
                  blockType: 'select-keyword-question';
                }
              | {
                  label: string;
                  required?: boolean | null;
                  selectOptions: {
                    label: string;
                    id?: string | null;
                  }[];
                  id?: string | null;
                  blockName?: string | null;
                  blockType: 'select-question';
                }
            )[];
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-yes-no-open-multiple-group-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            yesLabel?: string | null;
            noLabel?: string | null;
            /**
             * This group will be shown for a 'Yes' answer.
             */
            additionalRadioGroups: {
              label: string;
              required?: boolean | null;
              radioOptions: {
                label: string;
                id?: string | null;
              }[];
              id?: string | null;
            }[];
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-yes-no-open-additional-group-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            keywordCategory?:
              | (
                  | 'body_part'
                  | 'medicine'
                  | 'medicine_type'
                  | 'allergy_type'
                  | 'symptom_or_disease'
                  | 'test_vaccine_treatment'
                  | 'lifestyle'
                  | 'medical_science'
                  | 'other'
                )[]
              | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'multiple-select-dropdown-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            yesLabel?: string | null;
            noLabel?: string | null;
            /**
             * This note will be shown for a 'Yes' answer.
             */
            additionalNoteLabel: string;
            additionalNoteFields?:
              | (
                  | {
                      label: string;
                      required?: boolean | null;
                      id?: string | null;
                      blockName?: string | null;
                      blockType: 'plain-text-question';
                    }
                  | {
                      label: string;
                      required?: boolean | null;
                      /**
                       * Control how the user will input the number
                       */
                      type?: 'typing' | null;
                      id?: string | null;
                      blockName?: string | null;
                      blockType: 'input-number-question';
                    }
                )[]
              | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'radio-yes-no-open-additional-note-question';
          }
        | {
            group: (
              | {
                  label: string;
                  required?: boolean | null;
                  /**
                   * Control how the user will input the number
                   */
                  type?: 'typing' | null;
                  id?: string | null;
                  blockName?: string | null;
                  blockType: 'input-number-question';
                }
              | {
                  label: string;
                  required?: boolean | null;
                  id?: string | null;
                  blockName?: string | null;
                  blockType: 'plain-text-question';
                }
            )[];
            id?: string | null;
            blockName?: string | null;
            blockType: 'input-group-question';
          }
        | {
            label: string;
            description?: string | null;
            required?: boolean | null;
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'body-part-question';
          }
      )[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-search-histories".
 */
export interface UserSearchHistory {
  id: string;
  user?: (string | null) | User;
  facility?: (string | null) | Faculty;
  questions?:
    | {
        question?: (string | null) | Question;
        selectedKeywords?:
          | {
              keyword?: (string | null) | Keyword;
              editValue?: string | null;
              otherValue?: string | null;
              summarize?: string | null;
              id?: string | null;
            }[]
          | null;
        summarizeSentence?: string | null;
        id?: string | null;
      }[]
    | null;
  summaryAudio?:
    | {
        question?: (string | null) | Question;
        language?: ('vi' | 'ja') | null;
        audio?: (string | null) | Media;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "email-subscriptions".
 */
export interface EmailSubscription {
  id: string;
  email?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-subscriptions".
 */
export interface UserSubscription {
  id: string;
  user: string | User;
  subscription: string | Subscription;
  code?: string | null;
  startDate?: string | null;
  endDate?: string | null;
  transaction?: (string | null) | Transaction;
  status?: ('active' | 'canceled') | null;
  currentPaymentMethod?: ('bank_transfer' | 'qr' | 'stripe') | null;
  cancellationScheduled?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "transactions".
 */
export interface Transaction {
  id: string;
  transactionId: string;
  status?: ('pending_payment' | 'pending_approval' | 'completed' | 'canceled') | null;
  type?: ('RENEWAL' | 'SUBSCRIPTION_NEW ') | null;
  user?: (string | null) | User;
  plan?: (string | null) | Subscription;
  timestamp: string;
  paymentMethod?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  couponCode?: string | null;
  discount?: number | null;
  amount: number;
  total: number;
  evidenceImage?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-methods".
 */
export interface PaymentMethod {
  id: string;
  name: string;
  description?: string | null;
  accountNumber?: string | null;
  accountHolder?: string | null;
  bankName?: string | null;
  branch?: string | null;
  type?: ('bank_transfer' | 'qr' | 'stripe') | null;
  qrImage?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-examinations".
 */
export interface UserExamination {
  id: string;
  user: string | User;
  faculty: string | Faculty;
  examinationForm: string | ExaminationForm;
  patientName: string;
  patientDob: string;
  patientGender: 'male' | 'female';
  patientAge?: number | null;
  patientAddress: string;
  hasInsurance?: boolean | null;
  insuranceImage?: (string | null) | Media;
  formResult?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  submitted?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-keywords".
 */
export interface FavoriteKeyword {
  id: string;
  name?: string | null;
  user?: (string | null) | User;
  keyword: string | Keyword;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "image-search-conversations".
 */
export interface ImageSearchConversation {
  id: string;
  user: string | User;
  dailyMessageCount?: number | null;
  hasReachedLimit?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "image-search-messages".
 */
export interface ImageSearchMessage {
  id: string;
  conversation: string | ImageSearchConversation;
  sender: 'user' | 'bot';
  content:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  analyzeImage?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dynamic-popups".
 */
export interface DynamicPopup {
  id: string;
  name: string;
  content: (
    | {
        /**
         * Max 150 characters
         */
        title: string;
        /**
         * Max 150 characters
         */
        subtitle?: string | null;
        /**
         * Max 150 characters
         */
        description: string;
        bannerImage?: (string | null) | Media;
        action?: {
          title?: string | null;
          url?: string | null;
        };
        id?: string | null;
        blockName?: string | null;
        blockType: 'notification';
      }
    | {
        /**
         * Max 150 characters
         */
        title: string;
        /**
         * Max 150 characters
         */
        subtitle?: string | null;
        /**
         * Max 150 characters
         */
        description: string;
        bannerImage?: (string | null) | Media;
        action?: {
          title?: string | null;
          url?: string | null;
        };
        id?: string | null;
        blockName?: string | null;
        blockType: 'promotion';
      }
  )[];
  availabilityPeriod?: {
    startDate?: string | null;
    endDate?: string | null;
    isPopupActive?: boolean | null;
  };
  visibilitySettings: {
    displayPages: (
      | 'USER'
      | 'MEDICAL_HANDBOOK'
      | 'SUBSCRIPTION_MODIFY'
      | 'HOME'
      | 'SEARCH_SUMMARY'
      | 'LOGIN'
      | 'SIGNUP'
      | 'VERIFY'
      | 'RESET_PASSWORD'
      | 'RESEND_VERIFY_EMAIL'
      | 'TUTORIAL'
      | 'EXAMINATION'
      | 'POSTS'
      | 'PRODUCTS'
      | 'MEDICAL_DICTIONARY'
      | 'CHAT_BOT'
      | 'SUPPORT'
    )[];
    visibleTo: 'ALL' | 'LOGGED_USERS' | 'SPECIFIC_USERS';
    users?: (string | User)[] | null;
    delaySeconds?: number | null;
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "popup-tracking".
 */
export interface PopupTracking {
  id: string;
  user: string | User;
  popup: string | DynamicPopup;
  isViewed?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "coupon-tracking".
 */
export interface CouponTracking {
  id: string;
  user: string | User;
  coupon: string | Coupon;
  isApplied?: boolean | null;
  usageCount?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "facts".
 */
export interface Fact {
  id: string;
  content: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search-tips".
 */
export interface SearchTip {
  id: string;
  title: string;
  description: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "live-streams".
 */
export interface LiveStream {
  id: string;
  title: string;
  embedURL: string;
  watchURL: string;
  isActive?: boolean | null;
  startDate?: string | null;
  endDate?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "support-requests".
 */
export interface SupportRequest {
  id: string;
  email: string;
  reason: 'app_usage' | 'service_quality' | 'translation_quality' | 'system_error' | 'other';
  description: string;
  attachments?: (string | Media)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string;
  title: string;
  jaTitle?: string | null;
  description?: string | null;
  availableIn: (string | Subscription)[];
  type: 'MEDICINE' | 'DIETARY_SUPPLEMENT' | 'MEDICAL_INSTRUMENT';
  ageGroups?: (string | ProductAgeGroup)[] | null;
  medicineType?: (string | MedicineType)[] | null;
  categories: (string | ProductCategory)[];
  featured?: boolean | null;
  stores?:
    | {
        'medicine-store'?: (string | null) | MedicineBuyButton;
        url?: string | null;
        id?: string | null;
      }[]
    | null;
  heroImage: string | Media;
  uses?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  dosageForm?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  specification?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  ingredient?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  dosage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  contraindications?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  note?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  keywords?: (string | Keyword)[] | null;
  relatedProducts?: (string | Product)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  unverified?: boolean | null;
  AIAnalyzed?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  slug?: string | null;
  slugLock?: boolean | null;
  difyDocumentId?: string | null;
  mergedText?: string | null;
  /**
   * Use this field to trigger Dify document sync (support bulk edit)
   */
  triggerDifyDocumentSync?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-age-groups".
 */
export interface ProductAgeGroup {
  id: string;
  title: string;
  icon?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-categories".
 */
export interface ProductCategory {
  id: string;
  title: string;
  icon?: (string | null) | Media;
  parent?: (string | null) | ProductCategory;
  /**
   * Left boundary for nested set model (auto-calculated)
   */
  lft?: number | null;
  /**
   * Right boundary for nested set model (auto-calculated)
   */
  rgt?: number | null;
  /**
   * Category level (auto-calculated)
   */
  categoryLevel?: number | null;
  type: 'MEDICINE' | 'DIETARY_SUPPLEMENT' | 'MEDICAL_INSTRUMENT';
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-products".
 */
export interface FavoriteProduct {
  id: string;
  name?: string | null;
  user?: (string | null) | User;
  product: string | Product;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: string;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: string | Post;
        } | null);
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions".
 */
export interface FormSubmission {
  id: string;
  form: string | Form;
  submissionData?:
    | {
        field: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: string;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'posts';
    value: string | Post;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (string | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        id?: string | null;
        title?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: string;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug:
          | 'inline'
          | 'getAllUsers'
          | 'sendMailToUser'
          | 'cancelPlanSubscription'
          | 'cancelTransaction'
          | 'checkAndCancelUserSubscriptionTask'
          | 'countingSearchKeyword'
          | 'saveMedicineAnalyzedByAI'
          | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        parent?: {
          taskSlug?:
            | (
                | 'inline'
                | 'getAllUsers'
                | 'sendMailToUser'
                | 'cancelPlanSubscription'
                | 'cancelTransaction'
                | 'checkAndCancelUserSubscriptionTask'
                | 'countingSearchKeyword'
                | 'saveMedicineAnalyzedByAI'
                | 'schedulePublish'
              )
            | null;
          taskID?: string | null;
        };
        id?: string | null;
      }[]
    | null;
  workflowSlug?: ('resetConversationLimitWorkflow' | 'checkUserPlanWorkflow') | null;
  taskSlug?:
    | (
        | 'inline'
        | 'getAllUsers'
        | 'sendMailToUser'
        | 'cancelPlanSubscription'
        | 'cancelTransaction'
        | 'checkAndCancelUserSubscriptionTask'
        | 'countingSearchKeyword'
        | 'saveMedicineAnalyzedByAI'
        | 'schedulePublish'
      )
    | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'pages';
        value: string | Page;
      } | null)
    | ({
        relationTo: 'posts';
        value: string | Post;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'categories';
        value: string | Category;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'post-categories';
        value: string | PostCategory;
      } | null)
    | ({
        relationTo: 'medicines';
        value: string | Medicine;
      } | null)
    | ({
        relationTo: 'medicine-categories';
        value: string | MedicineCategory;
      } | null)
    | ({
        relationTo: 'medicine-buy-button';
        value: string | MedicineBuyButton;
      } | null)
    | ({
        relationTo: 'coupons';
        value: string | Coupon;
      } | null)
    | ({
        relationTo: 'kols';
        value: string | Kol;
      } | null)
    | ({
        relationTo: 'campaigns';
        value: string | Campaign;
      } | null)
    | ({
        relationTo: 'favorite-medicines';
        value: string | FavoriteMedicine;
      } | null)
    | ({
        relationTo: 'subscriptions';
        value: string | Subscription;
      } | null)
    | ({
        relationTo: 'login-sessions';
        value: string | LoginSession;
      } | null)
    | ({
        relationTo: 'versions';
        value: string | Version;
      } | null)
    | ({
        relationTo: 'symptoms';
        value: string | Symptom;
      } | null)
    | ({
        relationTo: 'keywords';
        value: string | Keyword;
      } | null)
    | ({
        relationTo: 'summarizes';
        value: string | Summarize;
      } | null)
    | ({
        relationTo: 'questions';
        value: string | Question;
      } | null)
    | ({
        relationTo: 'faculties';
        value: string | Faculty;
      } | null)
    | ({
        relationTo: 'body-parts';
        value: string | BodyPart;
      } | null)
    | ({
        relationTo: 'user-search-histories';
        value: string | UserSearchHistory;
      } | null)
    | ({
        relationTo: 'email-subscriptions';
        value: string | EmailSubscription;
      } | null)
    | ({
        relationTo: 'user-subscriptions';
        value: string | UserSubscription;
      } | null)
    | ({
        relationTo: 'transactions';
        value: string | Transaction;
      } | null)
    | ({
        relationTo: 'payment-methods';
        value: string | PaymentMethod;
      } | null)
    | ({
        relationTo: 'patient-group';
        value: string | PatientGroup;
      } | null)
    | ({
        relationTo: 'medicine-type';
        value: string | MedicineType;
      } | null)
    | ({
        relationTo: 'examination-forms';
        value: string | ExaminationForm;
      } | null)
    | ({
        relationTo: 'examination-questions';
        value: string | ExaminationQuestion;
      } | null)
    | ({
        relationTo: 'user-examinations';
        value: string | UserExamination;
      } | null)
    | ({
        relationTo: 'dietary-supplement-categories';
        value: string | DietarySupplementCategory;
      } | null)
    | ({
        relationTo: 'favorite-keywords';
        value: string | FavoriteKeyword;
      } | null)
    | ({
        relationTo: 'image-search-conversations';
        value: string | ImageSearchConversation;
      } | null)
    | ({
        relationTo: 'image-search-messages';
        value: string | ImageSearchMessage;
      } | null)
    | ({
        relationTo: 'dynamic-popups';
        value: string | DynamicPopup;
      } | null)
    | ({
        relationTo: 'popup-tracking';
        value: string | PopupTracking;
      } | null)
    | ({
        relationTo: 'coupon-tracking';
        value: string | CouponTracking;
      } | null)
    | ({
        relationTo: 'facts';
        value: string | Fact;
      } | null)
    | ({
        relationTo: 'search-tips';
        value: string | SearchTip;
      } | null)
    | ({
        relationTo: 'live-streams';
        value: string | LiveStream;
      } | null)
    | ({
        relationTo: 'support-requests';
        value: string | SupportRequest;
      } | null)
    | ({
        relationTo: 'products';
        value: string | Product;
      } | null)
    | ({
        relationTo: 'product-age-groups';
        value: string | ProductAgeGroup;
      } | null)
    | ({
        relationTo: 'product-categories';
        value: string | ProductCategory;
      } | null)
    | ({
        relationTo: 'favorite-products';
        value: string | FavoriteProduct;
      } | null)
    | ({
        relationTo: 'redirects';
        value: string | Redirect;
      } | null)
    | ({
        relationTo: 'forms';
        value: string | Form;
      } | null)
    | ({
        relationTo: 'form-submissions';
        value: string | FormSubmission;
      } | null)
    | ({
        relationTo: 'search';
        value: string | Search;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hero?:
    | T
    | {
        type?: T;
        richText?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        media?: T;
      };
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        formBlock?: T | FormBlockSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  categories?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock_select".
 */
export interface FormBlockSelect<T extends boolean = true> {
  form?: T;
  enableIntro?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  featured?: T;
  language?: T;
  heroImage?: T;
  content?: T;
  keywords?:
    | T
    | {
        keyword?: T;
        id?: T;
      };
  relatedPosts?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  authors?: T;
  populatedAuthors?:
    | T
    | {
        id?: T;
        name?: T;
      };
  categories?: T;
  slug?: T;
  slugLock?: T;
  mergedText?: T;
  difyDocumentId?: T;
  triggerDifyDocumentSync?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  roles?: T;
  subscription?: T;
  gender?: T;
  dob?: T;
  address?: T;
  settings?:
    | T
    | {
        fontSize?: T;
        fontWeight?: T;
      };
  lastLogin?: T;
  avatar?: T;
  connectedTo?: T;
  oauthAvatar?: T;
  subscriptionTime?:
    | T
    | {
        startDate?: T;
        endDate?: T;
      };
  sendExpiredNotificationTime?: T;
  sendExpiredWarningNotificationCount?: T;
  hasAppliedInvitationCode?: T;
  appleSub?: T;
  isDeactivated?: T;
  isDeactivatedAt?: T;
  archiveEmail?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  _verified?: T;
  _verificationToken?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "post-categories_select".
 */
export interface PostCategoriesSelect<T extends boolean = true> {
  title?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicines_select".
 */
export interface MedicinesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  availableIn?: T;
  isDietarySupplement?: T;
  patientGroup?: T;
  type?: T;
  featured?: T;
  stores?:
    | T
    | {
        'medicine-store'?: T;
        url?: T;
        id?: T;
      };
  heroImage?: T;
  uses?: T;
  dosageForm?: T;
  specification?: T;
  ingredient?: T;
  dosage?: T;
  contraindications?: T;
  note?: T;
  keywords?: T;
  relatedMedicines?: T;
  bodyPart?: T;
  categories?: T;
  dietarySupplementCategories?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  unverified?: T;
  AIAnalyzed?: T;
  slug?: T;
  slugLock?: T;
  difyDocumentId?: T;
  mergedText?: T;
  triggerDifyDocumentSync?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-categories_select".
 */
export interface MedicineCategoriesSelect<T extends boolean = true> {
  title?: T;
  bodyPart?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-buy-button_select".
 */
export interface MedicineBuyButtonSelect<T extends boolean = true> {
  title?: T;
  logo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "coupons_select".
 */
export interface CouponsSelect<T extends boolean = true> {
  code?: T;
  description?: T;
  amount?: T;
  used?: T;
  expiredAt?: T;
  isLimitedPerUser?: T;
  limit?: T;
  isExpired?: T;
  isActive?: T;
  campaign?: T;
  version?: T;
  type?: T;
  discountAmount?: T;
  discountType?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "kols_select".
 */
export interface KolsSelect<T extends boolean = true> {
  fullName?: T;
  phone?: T;
  email?: T;
  description?: T;
  introduceCode?: T;
  successSubscription?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "campaigns_select".
 */
export interface CampaignsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  startAt?: T;
  endAt?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-medicines_select".
 */
export interface FavoriteMedicinesSelect<T extends boolean = true> {
  name?: T;
  user?: T;
  medicine?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscriptions_select".
 */
export interface SubscriptionsSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  price?: T;
  duration?:
    | T
    | {
        amount?: T;
        unit?: T;
      };
  qrImage?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "login-sessions_select".
 */
export interface LoginSessionsSelect<T extends boolean = true> {
  isActive?: T;
  user?: T;
  expiredAt?: T;
  agentInfo?: T;
  device?: T;
  os?: T;
  browser?: T;
  deviceType?: T;
  isMobileLogin?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "versions_select".
 */
export interface VersionsSelect<T extends boolean = true> {
  name?: T;
  shortDescription?: T;
  content?: T;
  releasedAt?: T;
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "symptoms_select".
 */
export interface SymptomsSelect<T extends boolean = true> {
  name?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "keywords_select".
 */
export interface KeywordsSelect<T extends boolean = true> {
  name?: T;
  hiragana?: T;
  editable?: T;
  isCustom?: T;
  popupType?: T;
  description?: T;
  unverified?: T;
  categories?: T;
  relatedKeywords?: T;
  relatedImages?: T;
  audio?:
    | T
    | {
        language?: T;
        audio?: T;
        id?: T;
      };
  searchCount?: T;
  mergedText?: T;
  difyDocumentId?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "summarizes_select".
 */
export interface SummarizesSelect<T extends boolean = true> {
  name?: T;
  sentence?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "questions_select".
 */
export interface QuestionsSelect<T extends boolean = true> {
  name?: T;
  question?: T;
  key?: T;
  note?: T;
  keywords?: T;
  category?: T;
  isMultipleChoices?: T;
  canSkipQuestion?: T;
  summarize?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faculties_select".
 */
export interface FacultiesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  fallbackFaculty?: T;
  hasQuestions?: T;
  symptoms?: T;
  bodyParts?: T;
  subscription?: T;
  questions?:
    | T
    | {
        step?: T;
        question?: T;
        id?: T;
      };
  examinationForm?: T;
  icon?: T;
  mergedText?: T;
  difyDocumentId?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "body-parts_select".
 */
export interface BodyPartsSelect<T extends boolean = true> {
  name?: T;
  heroImage?: T;
  rootBodyPart?: T;
  childBodyParts?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-search-histories_select".
 */
export interface UserSearchHistoriesSelect<T extends boolean = true> {
  user?: T;
  facility?: T;
  questions?:
    | T
    | {
        question?: T;
        selectedKeywords?:
          | T
          | {
              keyword?: T;
              editValue?: T;
              otherValue?: T;
              summarize?: T;
              id?: T;
            };
        summarizeSentence?: T;
        id?: T;
      };
  summaryAudio?:
    | T
    | {
        question?: T;
        language?: T;
        audio?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "email-subscriptions_select".
 */
export interface EmailSubscriptionsSelect<T extends boolean = true> {
  email?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-subscriptions_select".
 */
export interface UserSubscriptionsSelect<T extends boolean = true> {
  user?: T;
  subscription?: T;
  code?: T;
  startDate?: T;
  endDate?: T;
  transaction?: T;
  status?: T;
  currentPaymentMethod?: T;
  cancellationScheduled?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "transactions_select".
 */
export interface TransactionsSelect<T extends boolean = true> {
  transactionId?: T;
  status?: T;
  type?: T;
  user?: T;
  plan?: T;
  timestamp?: T;
  paymentMethod?: T;
  couponCode?: T;
  discount?: T;
  amount?: T;
  total?: T;
  evidenceImage?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-methods_select".
 */
export interface PaymentMethodsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  accountNumber?: T;
  accountHolder?: T;
  bankName?: T;
  branch?: T;
  type?: T;
  qrImage?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-group_select".
 */
export interface PatientGroupSelect<T extends boolean = true> {
  title?: T;
  icon?: T;
  type?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medicine-type_select".
 */
export interface MedicineTypeSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  icon?: T;
  note?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "examination-forms_select".
 */
export interface ExaminationFormsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  step?:
    | T
    | {
        title?: T;
        questions?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "examination-questions_select".
 */
export interface ExaminationQuestionsSelect<T extends boolean = true> {
  name?: T;
  fields?:
    | T
    | {
        'radio-yes-no-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              yesLabel?: T;
              noLabel?: T;
              id?: T;
              blockName?: T;
            };
        'radio-group-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              radioOptions?:
                | T
                | {
                    label?: T;
                    type?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
        'radio-yes-no-open-checkbox-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              yesLabel?: T;
              noLabel?: T;
              openCheckboxLabel?: T;
              checkboxQuestions?:
                | T
                | {
                    checkboxLabel?: T;
                    checkboxQuestionLabel?: T;
                    checkboxQuestionDescription?: T;
                    keywordCategory?: T;
                    id?: T;
                  };
              noteLabel?: T;
              id?: T;
              blockName?: T;
            };
        'radio-yes-no-open-multiple-group-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              yesLabel?: T;
              noLabel?: T;
              openGroupQuestionsLabel?: T;
              openGroupQuestions?:
                | T
                | {
                    'plain-text-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          id?: T;
                          blockName?: T;
                        };
                    'select-keyword-question'?:
                      | T
                      | {
                          label?: T;
                          description?: T;
                          required?: T;
                          genderVisibility?: T;
                          type?: T;
                          keywordCategory?: T;
                          id?: T;
                          blockName?: T;
                        };
                    'select-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          selectOptions?:
                            | T
                            | {
                                label?: T;
                                id?: T;
                              };
                          id?: T;
                          blockName?: T;
                        };
                  };
              id?: T;
              blockName?: T;
            };
        'radio-yes-no-open-additional-group-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              yesLabel?: T;
              noLabel?: T;
              additionalRadioGroups?:
                | T
                | {
                    label?: T;
                    required?: T;
                    radioOptions?:
                      | T
                      | {
                          label?: T;
                          id?: T;
                        };
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
        'multiple-select-dropdown-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              keywordCategory?: T;
              id?: T;
              blockName?: T;
            };
        'radio-yes-no-open-additional-note-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              yesLabel?: T;
              noLabel?: T;
              additionalNoteLabel?: T;
              additionalNoteFields?:
                | T
                | {
                    'plain-text-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          id?: T;
                          blockName?: T;
                        };
                    'input-number-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          type?: T;
                          id?: T;
                          blockName?: T;
                        };
                  };
              id?: T;
              blockName?: T;
            };
        'input-group-question'?:
          | T
          | {
              group?:
                | T
                | {
                    'input-number-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          type?: T;
                          id?: T;
                          blockName?: T;
                        };
                    'plain-text-question'?:
                      | T
                      | {
                          label?: T;
                          required?: T;
                          id?: T;
                          blockName?: T;
                        };
                  };
              id?: T;
              blockName?: T;
            };
        'body-part-question'?:
          | T
          | {
              label?: T;
              description?: T;
              required?: T;
              genderVisibility?: T;
              id?: T;
              blockName?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-examinations_select".
 */
export interface UserExaminationsSelect<T extends boolean = true> {
  user?: T;
  faculty?: T;
  examinationForm?: T;
  patientName?: T;
  patientDob?: T;
  patientGender?: T;
  patientAge?: T;
  patientAddress?: T;
  hasInsurance?: T;
  insuranceImage?: T;
  formResult?: T;
  submitted?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dietary-supplement-categories_select".
 */
export interface DietarySupplementCategoriesSelect<T extends boolean = true> {
  title?: T;
  image?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-keywords_select".
 */
export interface FavoriteKeywordsSelect<T extends boolean = true> {
  name?: T;
  user?: T;
  keyword?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "image-search-conversations_select".
 */
export interface ImageSearchConversationsSelect<T extends boolean = true> {
  user?: T;
  dailyMessageCount?: T;
  hasReachedLimit?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "image-search-messages_select".
 */
export interface ImageSearchMessagesSelect<T extends boolean = true> {
  conversation?: T;
  sender?: T;
  content?: T;
  analyzeImage?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "dynamic-popups_select".
 */
export interface DynamicPopupsSelect<T extends boolean = true> {
  name?: T;
  content?:
    | T
    | {
        notification?:
          | T
          | {
              title?: T;
              subtitle?: T;
              description?: T;
              bannerImage?: T;
              action?:
                | T
                | {
                    title?: T;
                    url?: T;
                  };
              id?: T;
              blockName?: T;
            };
        promotion?:
          | T
          | {
              title?: T;
              subtitle?: T;
              description?: T;
              bannerImage?: T;
              action?:
                | T
                | {
                    title?: T;
                    url?: T;
                  };
              id?: T;
              blockName?: T;
            };
      };
  availabilityPeriod?:
    | T
    | {
        startDate?: T;
        endDate?: T;
        isPopupActive?: T;
      };
  visibilitySettings?:
    | T
    | {
        displayPages?: T;
        visibleTo?: T;
        users?: T;
        delaySeconds?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "popup-tracking_select".
 */
export interface PopupTrackingSelect<T extends boolean = true> {
  user?: T;
  popup?: T;
  isViewed?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "coupon-tracking_select".
 */
export interface CouponTrackingSelect<T extends boolean = true> {
  user?: T;
  coupon?: T;
  isApplied?: T;
  usageCount?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "facts_select".
 */
export interface FactsSelect<T extends boolean = true> {
  content?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search-tips_select".
 */
export interface SearchTipsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "live-streams_select".
 */
export interface LiveStreamsSelect<T extends boolean = true> {
  title?: T;
  embedURL?: T;
  watchURL?: T;
  isActive?: T;
  startDate?: T;
  endDate?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "support-requests_select".
 */
export interface SupportRequestsSelect<T extends boolean = true> {
  email?: T;
  reason?: T;
  description?: T;
  attachments?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products_select".
 */
export interface ProductsSelect<T extends boolean = true> {
  title?: T;
  jaTitle?: T;
  description?: T;
  availableIn?: T;
  type?: T;
  ageGroups?: T;
  medicineType?: T;
  categories?: T;
  featured?: T;
  stores?:
    | T
    | {
        'medicine-store'?: T;
        url?: T;
        id?: T;
      };
  heroImage?: T;
  uses?: T;
  dosageForm?: T;
  specification?: T;
  ingredient?: T;
  dosage?: T;
  contraindications?: T;
  note?: T;
  keywords?: T;
  relatedProducts?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  unverified?: T;
  AIAnalyzed?: T;
  slug?: T;
  slugLock?: T;
  difyDocumentId?: T;
  mergedText?: T;
  triggerDifyDocumentSync?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-age-groups_select".
 */
export interface ProductAgeGroupsSelect<T extends boolean = true> {
  title?: T;
  icon?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-categories_select".
 */
export interface ProductCategoriesSelect<T extends boolean = true> {
  title?: T;
  icon?: T;
  parent?: T;
  lft?: T;
  rgt?: T;
  categoryLevel?: T;
  type?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "favorite-products_select".
 */
export interface FavoriteProductsSelect<T extends boolean = true> {
  name?: T;
  user?: T;
  product?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms_select".
 */
export interface FormsSelect<T extends boolean = true> {
  title?: T;
  fields?:
    | T
    | {
        checkbox?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              defaultValue?: T;
              id?: T;
              blockName?: T;
            };
        country?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        email?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              message?: T;
              id?: T;
              blockName?: T;
            };
        number?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        select?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              placeholder?: T;
              options?:
                | T
                | {
                    label?: T;
                    value?: T;
                    id?: T;
                  };
              required?: T;
              id?: T;
              blockName?: T;
            };
        state?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        text?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        textarea?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
      };
  submitButtonLabel?: T;
  confirmationType?: T;
  confirmationMessage?: T;
  redirect?:
    | T
    | {
        url?: T;
      };
  emails?:
    | T
    | {
        emailTo?: T;
        cc?: T;
        bcc?: T;
        replyTo?: T;
        emailFrom?: T;
        subject?: T;
        message?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  form?: T;
  submissionData?:
    | T
    | {
        field?: T;
        value?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        id?: T;
        title?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        parent?:
          | T
          | {
              taskSlug?: T;
              taskID?: T;
            };
        id?: T;
      };
  workflowSlug?: T;
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-page-config".
 */
export interface HomePageConfig {
  id: string;
  searchTips?:
    | {
        title?: string | null;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  dailyVocabulary?: (string | Keyword)[] | null;
  didYouKnow?: string | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ai-bot-configs".
 */
export interface AiBotConfig {
  id: string;
  searchMedicineLimit?:
    | {
        plan: string | Subscription;
        limit: number;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-page-config_select".
 */
export interface HomePageConfigSelect<T extends boolean = true> {
  searchTips?:
    | T
    | {
        title?: T;
        description?: T;
        id?: T;
      };
  dailyVocabulary?: T;
  didYouKnow?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ai-bot-configs_select".
 */
export interface AiBotConfigsSelect<T extends boolean = true> {
  searchMedicineLimit?:
    | T
    | {
        plan?: T;
        limit?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetAllUsers".
 */
export interface TaskGetAllUsers {
  input?: unknown;
  output: {
    result?:
      | {
          id?: string | null;
          email?: string | null;
          name?: string | null;
          subscription?: (string | null) | Subscription;
          subscriptionTime?: {
            startDate?: string | null;
            endDate?: string | null;
          };
        }[]
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSendMailToUser".
 */
export interface TaskSendMailToUser {
  input: {
    email?: string | null;
    subject?: string | null;
    html?: string | null;
  };
  output: {
    status?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCancelPlanSubscription".
 */
export interface TaskCancelPlanSubscription {
  input: {
    userId?: string | null;
  };
  output: {
    status?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCancelTransaction".
 */
export interface TaskCancelTransaction {
  input: {
    transactionId?: string | null;
  };
  output: {
    message?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCheckAndCancelUserSubscriptionTask".
 */
export interface TaskCheckAndCancelUserSubscriptionTask {
  input: {
    userId?: string | null;
  };
  output: {
    message?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCountingSearchKeyword".
 */
export interface TaskCountingSearchKeyword {
  input: {
    keywordId?: string | null;
  };
  output: {
    message?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSaveMedicineAnalyzedByAI".
 */
export interface TaskSaveMedicineAnalyzedByAI {
  input: {
    name?: string | null;
    mediaId?: string | null;
    aiResponse?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output: {
    message?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?: {
      relationTo: 'dynamic-popups';
      value: string | DynamicPopup;
    } | null;
    global?: string | null;
    user?: (string | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowResetConversationLimitWorkflow".
 */
export interface WorkflowResetConversationLimitWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowCheckUserPlanWorkflow".
 */
export interface WorkflowCheckUserPlanWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "AdsenseBlock".
 */
export interface AdsenseBlock {
  slotId: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'adsense';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}
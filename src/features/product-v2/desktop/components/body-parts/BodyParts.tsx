import Image from 'next/image'

import BabyFaceIcon from '@/assets/icons/baby-face.svg'

const BodyParts: React.FC = () => {
  return (
    <div className="mt-3 grid grid-cols-6 gap-3">
      <div className="flex cursor-pointer flex-col items-center justify-center gap-3 rounded-md border border-transparent bg-neutral-50 p-3 hover:border-primary-500 hover:text-primary-500">
        <Image src={BabyFaceIcon} alt={'baby-face'} width={36} height={36} />
        <span className="typo-body-6">Đ<PERSON>u</span>
      </div>
    </div>
  )
}

export default BodyParts

import adultAndChildrenIcon from '@/assets/icons/adult-children-icon.svg'
import adultIcon from '@/assets/icons/adult-icon.svg'
import childrenIcon from '@/assets/icons/baby-face.svg'
import warningIcon from '@/assets/icons/warning-circle-primary.svg'
import { SheetClose } from '@/components/ui/Sheet/Sheet'
import { useAppLanguage } from '@/contexts/AppLanguageContext/AppLanguageContext'
import { MedicineType } from '@/payload-types'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'
export const ProductListNotePopup: React.FC<{
  types: MedicineType[]
  isSupplementProduct: boolean
}> = ({ types, isSupplementProduct }) => {
  const t = useTranslations()

  const locale = useLocale()
  const { secondaryLanguage: secondaryLang } = useAppLanguage()

  return (
    <>
      <div className="flex flex-col gap-3">
        <div className="flex items-center gap-2">
          <Image
            src={warningIcon}
            alt="warning-icon"
            width={16}
            height={16}
            className="size-4 shrink-0"
          ></Image>
          <p className="typo-body-6 text-informative-700">{t('MES-491')}</p>
        </div>

        <div className="flex max-h-[540px] flex-col gap-2 overflow-y-auto">
          <div className="flex flex-col gap-2">
            <div className="typo-body-10 py-1">{t('MES-482')}</div>

            <div className="flex gap-3">
              <div className="flex h-6 w-6 min-w-[24px] items-center justify-center">
                <Image
                  src={childrenIcon}
                  alt="children-icon"
                  width={24}
                  height={24}
                  className="shrink-0"
                ></Image>
              </div>
              <div>
                <div className="typo-body-6">{t('MES-492')}</div>
                <div className="typo-body-9 text-danger-600">{t('MES-542')}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex h-6 w-6 items-center justify-center">
                <Image
                  src={adultAndChildrenIcon}
                  alt="children-icon"
                  width={24}
                  height={24}
                  className="shrink-0"
                ></Image>
              </div>
              <p className="typo-body-6">{t('MES-539')}</p>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex h-6 w-6 items-center justify-center">
                <Image
                  src={adultIcon}
                  alt="children-icon"
                  width={24}
                  height={24}
                  className="shrink-0"
                ></Image>
              </div>
              <p className="typo-body-6">{t('MES-540')}</p>
            </div>
          </div>

          {!isSupplementProduct && (
            <>
              <div className="h-[1px] w-full bg-divider"></div>

              <div className="flex flex-col gap-2">
                <div className="typo-body-10 py-1">{t('MES-541')}</div>

                {types.map((t) => (
                  <div className="flex gap-3" key={t.id}>
                    <div className="relative h-[24px] min-h-6 w-[24px] min-w-6">
                      {t?.icon && typeof t.icon !== 'string' && t.icon?.url && (
                        <Image
                          src={t?.icon?.url}
                          alt={t?.name}
                          fill
                          className="h-full w-full"
                          sizes="300px"
                        ></Image>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      <div className="typo-body-6">{`${t.name[locale]} (${t.name[secondaryLang]})`}</div>
                      <div className="typo-body-9">{t.note ? t.note[locale] : ''}</div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      <div className="mt-3 flex justify-end gap-4">
        <SheetClose className="typo-button-3 flex w-full justify-center rounded-[6px] border-0 bg-primary px-6 py-2 text-white">
          {t('MES-352')}
        </SheetClose>
      </div>
    </>
  )
}
